#ifndef _USER_APP_CONVERSION_H_
#define _USER_APP_CONVERSION_H_

/**
 * @file user_app_conversion.h
 * @brief BMS数据转换函数库
 * @version 2.0
 * @date 2024
 *
 * 本文件提供了BMS系统中各种数据转换功能，包括：
 * - NTC温度传感器数据转换
 * - 电流ADC数据转换和温度补偿
 * - 数字滤波器（混合SMA+EMA）
 * - 电压ADC数据转换
 * - SOC（电量状态）计算
 *
 * 所有新版本函数都支持自定义参数，提供更好的模块化和可配置性。
 * 保留了原有函数接口以确保向后兼容性。
 */
#define NTC_TABLE_SIZE (sizeof(ntc3435) / sizeof(ntc3435[0]))
/* ==========================================数据结构定义========================================== */

/**
 * @brief NTC阻值-温度对照表结构体
 * @note 用于存储NTC热敏电阻的温度-阻值对应关系
 */
typedef struct {
    unsigned char temp;      /* 温度值，单位：℃，通常加100偏移存储 */
    double resist;          /* 对应阻值，单位：kΩ */
} NTC_TypeDef;


/**
 * @brief 滤波器状态结构体（内部使用）
 * @note 用于存储混合滤波器的运行状态
 */
typedef struct {
    float *sma_buffer;          /* SMA缓冲区指针 */
    unsigned short buffer_size; /* 缓冲区大小 */
    unsigned short sma_index;   /* SMA索引 */
    float ema_value;           /* EMA当前值 */
    unsigned char initialized; /* 初始化标志 */
} FilterState;

/* ==========================================NTC温度转换模块========================================== */

/**
 * @brief NTC温度转换函数（新版本）
 * @note 支持自定义温度偏移和查找表，提供更好的灵活性
 */

/* 电阻值转温度 */
extern char ntc_calculate_temp_from_resistance(double resistance_ohm, char temp_offset_celsius,
                                              const NTC_TypeDef *ntc_table, unsigned short table_size);

/* ADC寄存器值转温度（简化版本） */
extern char ntc_calculate_temp_from_adc(unsigned short adc_reg_value, const NTC_TypeDef *ntc_table, unsigned short table_size);

/* 低温保护寄存器值转温度（简化版本） */
extern char ntc_calculate_low_temp_from_reg(unsigned char reg_value, const NTC_TypeDef *ntc_table, unsigned short table_size);

/* 外部温度寄存器值转温度（简化版本） */
extern char ntc_calculate_external_temp(unsigned short temp_reg, const NTC_TypeDef *ntc_table, unsigned short table_size);

/* 温度转电阻值 */
extern double ntc_find_resistance_from_temp(char temp_celsius, char temp_offset_celsius,
                                           const NTC_TypeDef *ntc_table, unsigned short table_size);

/* 温度转高温保护寄存器值（简化版本） */
extern unsigned char ntc_calculate_high_temp_reg(char temp_celsius, const NTC_TypeDef *ntc_table, unsigned short table_size);

/* 温度转低温保护寄存器值（简化版本） */
extern unsigned char ntc_calculate_low_temp_reg(char temp_celsius, const NTC_TypeDef *ntc_table, unsigned short table_size);



/* ==========================================电流转换模块========================================== */

/**
 * @brief 电流转换函数（新版本）
 * @note 支持自定义采样电阻和温度补偿参数
 */

/* ADC寄存器值转电流 */
extern int current_calculate_from_adc(unsigned short adc_reg_value, float sampling_resistance_mohm,
                                     float adc_gain, float adc_reference_factor);

/* 电流温度补偿 */
extern float current_apply_temp_compensation(float current_ma, float temp_celsius,
                                            float reference_temp_celsius, float temp_coefficient,
                                            float temp_min_celsius, float temp_max_celsius);



/* ==========================================滤波器模块========================================== */
/**
 * @brief 数字滤波器函数（兼容版本）
 * @note 保持原有接口，使用全局状态
 */
extern float hybridFilter(float new_sample);

/* ==========================================电压转换模块========================================== */



/**
 * @brief 电压转换函数（兼容版本）
 * @note 保持原有接口，内部调用新版本函数
 */
extern unsigned short Reg_From_Voltage(unsigned short cell);

/* ==========================================SOC计算模块========================================== */

/**
 * @brief SOC计算函数（新版本）
 * @note 支持充放电效率和自定义参数
 */



extern const NTC_TypeDef ntc3435[150];
/* ==========================================使用示例========================================== */
extern float update_soc_simple(float soc, float capacity_mAh, float current_mA, float time_ms, unsigned char direction);

#endif /* _USER_APP_CONVERSION_H_ */