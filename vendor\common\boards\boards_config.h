/********************************************************************************************************
 * @file    boards_config.h
 *
 * @brief   This is the header file for BLE SDK
 *
 * <AUTHOR> GROUP
 * @date    06,2022
 *
 * @par     Copyright (c) 2022, Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *
 *******************************************************************************************************/
#pragma once

///////////////////////Board Select Configuration ///////////////////////////////
#ifndef BOARD_825X_EVK_C1T139A30
#define BOARD_825X_EVK_C1T139A30						0x9A30     //
#endif
#ifndef BOARD_825X_DONGLE_C1T139A3
#define BOARD_825X_DONGLE_C1T139A3						0x39A3     //
#endif
#ifndef BOARD_825X_RCU_C1T139A5
#define BOARD_825X_RCU_C1T139A5							0x39A5     //
#endif
#ifndef BOARD_827X_EVK_C1T197A30
#define BOARD_827X_EVK_C1T197A30						0x7A30	  //
#endif
#ifndef BOARD_827X_DONGLE_C1T201A3
#define BOARD_827X_DONGLE_C1T201A3						0x01A3	  //
#endif
#ifndef BOARD_827X_RCU_C1T197A5
#define BOARD_827X_RCU_C1T197A5							0x97A5     //
#endif
