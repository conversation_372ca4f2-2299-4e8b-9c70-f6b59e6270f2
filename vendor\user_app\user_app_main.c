#include "user_app_main.h"
#include "bms/zy/sh367601xb.h"

#define USER_APP_ZERO_COUNT 10        /* 零点采样次数 */
/* 队列 */
static Queue q;
static __attribute__((used)) QueueNode node;
static __attribute__((used)) int read_time;
static __attribute__((used)) char sh367601x_task_lock;
/* 全局数据 */
User_App_Ble_TypeDef  Ble;
User_App_Uart_TypeDef Uart;
SH367601B_Device sh3676010b;




/* 用户app初始化 */
void User_App_Init(void)
{
    /* 分路串口开关初始化 */
    User_App_Rs2058_Init();

    sh367601b_init(&sh3676010b);
    

    /* 添加开机任务 */
    queue_push(&q, QUEUE_ROM1_TASK, QUEUE_ROM1_TIME);
    /* 定时器初始化 */
    timer0_set_mode(TIMER_MODE_SYSCLK,0, 20 * CLOCK_SYS_CLOCK_1MS);
    timer1_set_mode(TIMER_MODE_SYSCLK,0, 2000 * CLOCK_SYS_CLOCK_1MS);
	timer_start(TIMER0);
	timer_start(TIMER1);
    printf("Init Completed\n");
}
/* 定时器回调函数 */
void time0_callback(void)
{
    if (true == sh367601x_task_lock) return;
    queue_push(&q, QUEUE_CURR_TASK, QUEUE_CURR_TIME);
}
void time1_callback(void)
{
    queue_push(&q, QUEUE_RAM1_TASK, QUEUE_RAM1_TIME);
}


/* 蓝牙数据处理 */
void User_App_Sh367601x_Ble_process(unsigned char *data, unsigned char len)
{
    // tlkapi_send_string_data(APP_LOG_EN,"[USER][LOG] gatt receive = ", data, len);
    if (0xAA == data[0] && len >= 4)
    {
        // switch (data[1])
        // {
        //     /* 电压校准（写） */
        //     case 0x02:
        //     {
        //         u8 cmd[5] = {0}, flg = USER_APP_FLASH_FLG, batt_num = System.Voltage.num[0] + System.Voltage.num[1];
        //         for (unsigned short i = 0; i < batt_num; i++)
        //             System.Parameters.voltage[i] = (data[3 + (i * 2)] << 8) | data[4 + (i * 2)];
        //         My_Flash_Write(FLASH_ADDR_VOLTAGE, &data[3], 48);
        //         My_Flash_Write(FLASH_ADDR_VOLTAGE_FLG, &flg, 1);
        //         cmd[0] = 0xAA;
        //         cmd[1] = data[1];
        //         cmd[2] = 0x00;
        //         cmd[3] = 0x00;
        //         cmd[4] = 0xFF;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 采样电阻（写） */
        //     case 0x03:
        //     {
        //         u8 cmd[5] = {0}, flg = USER_APP_FLASH_FLG;
        //         System.Current.sampling_r = ((data[3] << 8) | data[4]) / (float)1000000.0;
        //         My_Flash_Write(FLASH_ADDR_RESISTANCE, &data[3], 2);
        //         My_Flash_Write(FLASH_ADDR_RESISTANCE_FLG, &flg, 1);
        //         cmd[0] = 0xAA;
        //         cmd[1] = data[1];
        //         cmd[2] = 0x00;
        //         cmd[3] = 0x00;
        //         cmd[4] = 0xFF;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 读取电压 */
        //     case 0x04:
        //     {
        //         u8 cmd[52], batt_num = System.Voltage.num[0] + System.Voltage.num[1];;
        //         memcpy(&cmd[3], (char *)&System.Voltage.single_voltage[0], batt_num * 2);
        //         for (unsigned short i = 0; i < batt_num; i++)
        //         {
        //             cmd[3 + (i * 2)] = System.Parameters.voltage[i] >> 8;
        //             cmd[4 + (i * 2)] = System.Parameters.voltage[i];
        //         }
        //         cmd[0] = 0xAA;
        //         cmd[1] = data[1];
        //         cmd[2] = 0x30;
        //         cmd[51] = 0xFF;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 读取电流 */
        //     case 0x05:
        //     {
        //         u8 cmd[6];
        //         u16 sampling_r = System.Current.sampling_r * 1000000;
        //         sampling_r = BE16_TO_LE16(sampling_r);
        //         memcpy(&cmd[3], &sampling_r, 2);
        //         cmd[0] = 0xAA;
        //         cmd[1] = data[1];
        //         cmd[2] = 0x02;
        //         cmd[5] = 0xFF;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 电池容量（读） */
        //     case 0x10:
        //     {
        //         u8 cmd[12];
        //         u32 capacity = BE32_TO_LE32(System.Soc.total_capacity);
        //         memcpy(&cmd[3], &capacity, 4);
        //         cmd[0] = 0xAA;
        //         cmd[1] = data[1];
        //         cmd[2] = 0x04;
        //         cmd[7] = 0x00;
        //         cmd[8] = 0x00;
        //         cmd[9] = 0x00;
        //         cmd[10] = 0x00;
        //         cmd[11] = 0xFF;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 电池容量（写） */
        //     case 0x11:
        //     {
        //         u8 cmd[5] = {0}, flg = USER_APP_FLASH_FLG;
        //         u32 curr_capacity = (data[7] << 24) | (data[8] << 16) | (data[9] << 8) | data[10];
        //         System.Soc.total_capacity = (data[3] << 24) | (data[4] << 16) | (data[5] << 8) | data[6];
        //         My_Flash_Write(FLASH_ADDR_BATT_CAPACITY, &data[3], 8);
        //         My_Flash_Write(FLASH_ADDR_BATT_CAPACITY_FLG, &flg, 1);
        //         System.Soc.soc = (float)((curr_capacity * 100.0) / System.Soc.total_capacity);
        //         cmd[0] = 0xAA;
        //         cmd[1] = data[1];
        //         cmd[2] = 0x00;
        //         cmd[3] = 0x00;
        //         cmd[4] = 0xFF;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 零点校准 */
        //     case 0x12:
        //     {
        //     	u8 cmd[5];
        //         System.Parameters.zero_flg = true;
        //         cmd[0] = 0xAA;
        //         cmd[1] = data[1];
        //         cmd[2] = 0x00;
        //         cmd[3] = 0x00;
        //         cmd[4] = 0xFF;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 写rom产品信息 */
        //     case 0x13:
        //     {
        //         char cn = data[4] - 10;
        //         unsigned char cmd[6];
        //         Sh3607601x.write_rom[0] = Sh3607601x.rom[0];
        //         Sh3607601x.write_rom[1] = Sh3607601x.rom[1];
        //         User_App_Write_Id(&Sh3607601x.write_rom[0], data[3]);
        //         User_App_Write_Cn(&Sh3607601x.write_rom[0], 10);
        //         Write_Rom_To_Eeprom(write_rom_data[0], &Sh3607601x.write_rom[0]);
        //         User_App_Add_Write1_Cmd();
        //         User_App_Write_Id(&Sh3607601x.write_rom[1], data[3]);
        //         User_App_Write_Cn(&Sh3607601x.write_rom[1], cn);
        //         Write_Rom_To_Eeprom(write_rom_data[1], &Sh3607601x.write_rom[1]);
        //         User_App_Add_Write2_Cmd();
        //         queue_push(&q, QUEUE_ROM1_TASK, QUEUE_ROM1_TIME);
        //         queue_push(&q, QUEUE_ROM2_TASK, QUEUE_ROM2_TIME);
        //         cmd[0] = 0xAA;
        //         cmd[1] = data[1];
        //         cmd[2] = 0x02;
        //         cmd[3] = 0x00;
        //         cmd[4] = 0x00;
        //         cmd[5] = 0xFF;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 写rom系统配置 */
        //     case 0x14:
        //     {
        //         unsigned char cmd[6];
        //         Sh3607601x.write_rom[0] = Sh3607601x.rom[0];
        //         Sh3607601x.write_rom[1] = Sh3607601x.rom[1];
        //         User_App_Write_Enmos( &Sh3607601x.write_rom[0], data[3]);
        //         User_App_Write_Enmosr(&Sh3607601x.write_rom[0], data[4]);
        //         User_App_Write_Chys(  &Sh3607601x.write_rom[0], data[5]);
        //         User_App_Write_Bals(  &Sh3607601x.write_rom[0], data[6]);
        //         User_App_Write_Chs(   &Sh3607601x.write_rom[0], data[7]);
        //         User_App_Write_Ocra(  &Sh3607601x.write_rom[0], data[8]);
        //         User_App_Write_Eovr(  &Sh3607601x.write_rom[0], data[9]);
        //         User_App_Write_Euvr(  &Sh3607601x.write_rom[0], data[10]);
        //         User_App_Write_Eow(   &Sh3607601x.write_rom[0], data[11]);
        //         User_App_Write_Eot3(  &Sh3607601x.write_rom[0], data[12]);
        //         Write_Rom_To_Eeprom(write_rom_data[0], &Sh3607601x.write_rom[0]);
        //         User_App_Add_Write1_Cmd();
        //         User_App_Write_Enmos( &Sh3607601x.write_rom[1], data[3]);
        //         User_App_Write_Enmosr(&Sh3607601x.write_rom[1], data[4]);
        //         User_App_Write_Chys(  &Sh3607601x.write_rom[1], data[5]);
        //         User_App_Write_Bals(  &Sh3607601x.write_rom[1], data[6]);
        //         User_App_Write_Chs(   &Sh3607601x.write_rom[1], data[7]);
        //         User_App_Write_Ocra(  &Sh3607601x.write_rom[1], data[8]);
        //         User_App_Write_Eovr(  &Sh3607601x.write_rom[1], data[9]);
        //         User_App_Write_Euvr(  &Sh3607601x.write_rom[1], data[10]);
        //         User_App_Write_Eow(   &Sh3607601x.write_rom[1], data[11]);
        //         User_App_Write_Eot3(  &Sh3607601x.write_rom[1], data[12]);
        //         Write_Rom_To_Eeprom(write_rom_data[1], &Sh3607601x.write_rom[1]);
        //         User_App_Add_Write2_Cmd();
        //         queue_push(&q, QUEUE_ROM1_TASK, QUEUE_ROM1_TIME);
        //         queue_push(&q, QUEUE_ROM2_TASK, QUEUE_ROM2_TIME);
        //         cmd[0] = 0xAA;
        //         cmd[1] = data[1];
        //         cmd[2] = 0x02;
        //         cmd[3] = 0x00;
        //         cmd[4] = 0x00;
        //         cmd[5] = 0xFF;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 写rom电压保护 */
        //     case 0x15:
        //     {
        //         unsigned char cmd[6];
        //         Sh3607601x.write_rom[0] = Sh3607601x.rom[0];
        //         Sh3607601x.write_rom[1] = Sh3607601x.rom[1];
        //         User_App_Write_Ov(  &Sh3607601x.write_rom[0], (data[3] << 8) | data[4]);
        //         User_App_Write_Ovt( &Sh3607601x.write_rom[0], data[5]);
        //         User_App_Write_Ovr( &Sh3607601x.write_rom[0], (data[6] << 8) | data[7]);
        //         User_App_Write_Balv(&Sh3607601x.write_rom[0], (data[8] << 8) | data[9]);
        //         User_App_Write_Bald(&Sh3607601x.write_rom[0], data[10]);
        //         User_App_Write_Balt(&Sh3607601x.write_rom[0], data[11]);
        //         User_App_Write_Uvr( &Sh3607601x.write_rom[0], (data[12] << 8) | data[13]);
        //         User_App_Write_Uv(  &Sh3607601x.write_rom[0], (data[14] << 8) | data[15]);
        //         User_App_Write_Uvt( &Sh3607601x.write_rom[0], data[16]);
        //         User_App_Write_Lov( &Sh3607601x.write_rom[0], (data[17] << 8) | data[18]);
        //         Write_Rom_To_Eeprom(write_rom_data[0], &Sh3607601x.write_rom[0]);
        //         User_App_Add_Write1_Cmd();
        //         User_App_Write_Ov(  &Sh3607601x.write_rom[1], (data[3] << 8) | data[4]);
        //         User_App_Write_Ovt( &Sh3607601x.write_rom[1], data[5]);
        //         User_App_Write_Ovr( &Sh3607601x.write_rom[1], (data[6] << 8) | data[7]);
        //         User_App_Write_Balv(&Sh3607601x.write_rom[1], (data[8] << 8) | data[9]);
        //         User_App_Write_Bald(&Sh3607601x.write_rom[1], data[10]);
        //         User_App_Write_Balt(&Sh3607601x.write_rom[1], data[11]);
        //         User_App_Write_Uvr( &Sh3607601x.write_rom[1], (data[12] << 8) | data[13]);
        //         User_App_Write_Uv(  &Sh3607601x.write_rom[1], (data[14] << 8) | data[15]);
        //         User_App_Write_Uvt( &Sh3607601x.write_rom[1], data[16]);
        //         User_App_Write_Lov( &Sh3607601x.write_rom[1], (data[17] << 8) | data[18]);
        //         Write_Rom_To_Eeprom(write_rom_data[1], &Sh3607601x.write_rom[1]);
        //         User_App_Add_Write2_Cmd();
        //         queue_push(&q, QUEUE_ROM1_TASK, QUEUE_ROM1_TIME);
        //         queue_push(&q, QUEUE_ROM2_TASK, QUEUE_ROM2_TIME);
        //         cmd[0] = 0xAA;
        //         cmd[1] = data[1];
        //         cmd[2] = 0x02;
        //         cmd[3] = 0x00;
        //         cmd[4] = 0x00;
        //         cmd[5] = 0xFF;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 写rom电流保护 */
        //     case 0x16:
        //     {
        //         unsigned short ocd1v, occv;
        //         unsigned char cmd[6];
        //         Sh3607601x.write_rom[0] = Sh3607601x.rom[0];
        //         Sh3607601x.write_rom[1] = Sh3607601x.rom[1];
        //         ocd1v = (data[3] << 8) | data[4];
        //         occv = (data[11] << 8) | data[12];
        //         User_App_Write_Ocd1v(&Sh3607601x.write_rom[0], ocd1v);
        //         User_App_Write_Ocd1t(&Sh3607601x.write_rom[0], data[5]);
        //         User_App_Write_Ocd2v(&Sh3607601x.write_rom[0], data[6]);
        //         User_App_Write_Ocd2t(&Sh3607601x.write_rom[0], data[7]);
        //         User_App_Write_Sct(  &Sh3607601x.write_rom[0], data[10]);
        //         User_App_Write_Occv( &Sh3607601x.write_rom[0], occv);
        //         User_App_Write_Occt( &Sh3607601x.write_rom[0], data[13]);
        //         Write_Rom_To_Eeprom(write_rom_data[0], &Sh3607601x.write_rom[0]);
        //         User_App_Add_Write1_Cmd();
        //         User_App_Write_Ocd1v(&Sh3607601x.write_rom[1], ocd1v);
        //         User_App_Write_Ocd1t(&Sh3607601x.write_rom[1], data[5]);
        //         User_App_Write_Ocd2v(&Sh3607601x.write_rom[1], data[6]);
        //         User_App_Write_Ocd2t(&Sh3607601x.write_rom[1], data[7]);
        //         User_App_Write_Sct(  &Sh3607601x.write_rom[1], data[10]);
        //         User_App_Write_Occv( &Sh3607601x.write_rom[1], occv);
        //         User_App_Write_Occt( &Sh3607601x.write_rom[1], data[13]);
        //         Write_Rom_To_Eeprom(write_rom_data[1], &Sh3607601x.write_rom[1]);
        //         User_App_Add_Write2_Cmd();
        //         queue_push(&q, QUEUE_ROM1_TASK, QUEUE_ROM1_TIME);
        //         queue_push(&q, QUEUE_ROM2_TASK, QUEUE_ROM2_TIME);
        //         cmd[0] = 0xAA;
        //         cmd[1] = data[1];
        //         cmd[2] = 0x02;
        //         cmd[3] = 0x00;
        //         cmd[4] = 0x00;
        //         cmd[5] = 0xFF;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 写rom温度保护 */
        //     case 0x17:
        //     {
        //         u8 cmd[6];
        //         Sh3607601x.write_rom[0] = Sh3607601x.rom[0];
        //         Sh3607601x.write_rom[1] = Sh3607601x.rom[1];
        //         User_App_Write_Otc( &Sh3607601x.write_rom[0], data[3]);
        //         User_App_Write_Otcr(&Sh3607601x.write_rom[0], data[4]);
        //         User_App_Write_Utc( &Sh3607601x.write_rom[0], data[5]);
        //         User_App_Write_Utcr(&Sh3607601x.write_rom[0], data[6]);
        //         User_App_Write_Otd( &Sh3607601x.write_rom[0], data[7]);
        //         User_App_Write_Otdr(&Sh3607601x.write_rom[0], data[8]);
        //         User_App_Write_Utd( &Sh3607601x.write_rom[0], data[9]);
        //         User_App_Write_Utdr(&Sh3607601x.write_rom[0], data[10]);
        //         User_App_Write_Tc(  &Sh3607601x.write_rom[0], data[11]);
        //         Write_Rom_To_Eeprom(write_rom_data[0], &Sh3607601x.write_rom[0]);
        //         User_App_Add_Write1_Cmd();
        //         User_App_Write_Otc( &Sh3607601x.write_rom[1], data[3]);
        //         User_App_Write_Otcr(&Sh3607601x.write_rom[1], data[4]);
        //         User_App_Write_Utc( &Sh3607601x.write_rom[1], data[5]);
        //         User_App_Write_Utcr(&Sh3607601x.write_rom[1], data[6]);
        //         User_App_Write_Otd( &Sh3607601x.write_rom[1], data[7]);
        //         User_App_Write_Otdr(&Sh3607601x.write_rom[1], data[8]);
        //         User_App_Write_Utd( &Sh3607601x.write_rom[1], data[9]);
        //         User_App_Write_Utdr(&Sh3607601x.write_rom[1], data[10]);
        //         User_App_Write_Tc(  &Sh3607601x.write_rom[1], data[11]);
        //         Write_Rom_To_Eeprom(write_rom_data[1], &Sh3607601x.write_rom[1]);
        //         User_App_Add_Write2_Cmd();
        //         queue_push(&q, QUEUE_ROM1_TASK, QUEUE_ROM1_TIME);
        //         queue_push(&q, QUEUE_ROM2_TASK, QUEUE_ROM2_TIME);
        //         cmd[0] = 0xAA;
        //         cmd[1] = data[1];
        //         cmd[2] = 0x02;
        //         cmd[3] = 0x00;
        //         cmd[4] = 0x00;
        //         cmd[5] = 0xFF;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 读rom产品信息 */ 
        //     case 0x18:
        //     {
        //         u8 cmd[6];
        //         cmd[0] = 0xAA;
        //         cmd[1] = data[1];
        //         cmd[2] = 0x02;
        //         cmd[5] = 0xFF;
        //         cmd[3] = Sh3607601x.rom[0].id;
        //         cmd[4] = System.Voltage.num[0] + System.Voltage.num[1];
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 读rom系统配置 */
        //     case 0x19:
        //     {
        //         u8 cmd[14];
        //         cmd[0] =  0xAA;
        //         cmd[1] =  data[1];
        //         cmd[2] =  0x0A;
        //         cmd[13] = 0xFF;
        //         cmd[3] =  Sh3607601x.rom[0].enmos;
        //         cmd[4] =  Sh3607601x.rom[0].enmosr;
        //         cmd[5] =  Sh3607601x.rom[0].chys;
        //         cmd[6] =  Sh3607601x.rom[0].bals;
        //         cmd[7] =  Sh3607601x.rom[0].chs;
        //         cmd[8] =  Sh3607601x.rom[0].ocra;
        //         cmd[9] =  Sh3607601x.rom[0].eovr;
        //         cmd[10] = Sh3607601x.rom[0].euvr;
        //         cmd[11] = Sh3607601x.rom[0].eow;
        //         cmd[12] = Sh3607601x.rom[0].eot3;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 读rom电压保护 */
        //     case 0x20:
        //     {
        //         u8 cmd[20];
        //         u16 temp;
        //         cmd[0]  = 0xAA;
        //         cmd[1]  = data[1];
        //         cmd[2]  = 0x10;
        //         cmd[19] = 0xFF;
        //         temp = User_App_Conversion_Ov(Sh3607601x.rom[0].ov);
        //         cmd[3] = (temp >> 8);
        //         cmd[4] = temp;
        //         cmd[5] = Sh3607601x.rom[0].ovt;
        //         temp = User_App_Conversion_Ovr(Sh3607601x.rom[0].ovr);
        //         cmd[6] = temp >> 8;
        //         cmd[7] = temp;
        //         temp = User_App_Conversion_Balv(Sh3607601x.rom[0].balv);
        //         cmd[8] = temp >> 8;
        //         cmd[9] = temp;
        //         cmd[10] = Sh3607601x.rom[0].bald;
        //         cmd[11] = Sh3607601x.rom[0].balt;
        //         temp = User_App_Conversion_Uvr(Sh3607601x.rom[0].uvr);
        //         cmd[12] = temp >> 8;
        //         cmd[13] = temp;
        //         temp = User_App_Conversion_Uv(Sh3607601x.rom[0].uv);
        //         cmd[14] = temp >> 8;
        //         cmd[15] = temp;
        //         cmd[16] = Sh3607601x.rom[0].uvt;
        //         temp = User_App_Conversion_Lov(Sh3607601x.rom[0].lov);
        //         cmd[17] = temp >> 8;
        //         cmd[18] = temp;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 读rom电流保护 */
        //     case 0x21:
        //     {
        //         u8 cmd[15];
        //         u16 temp;
        //         cmd[0]  = 0xAA;
        //         cmd[1]  = data[1];
        //         cmd[2]  = 0x0C;
        //         cmd[14] = 0xFF;
        //         temp = User_App_Conversion_Ocd1v(Sh3607601x.rom[0].ocd1v);
        //         cmd[3] = (temp >> 8);
        //         cmd[4] = temp;
        //         cmd[5] = Sh3607601x.rom[0].ocd1t;
        //         cmd[6] = Sh3607601x.rom[0].ocd2v;
        //         cmd[7] = Sh3607601x.rom[0].ocd2t;
        //         temp = User_App_Conversion_Ocd2v(Sh3607601x.rom[0].ocd2v);
        //         temp *= 2;
        //         cmd[8] = temp >> 8;
        //         cmd[9] = temp;
        //         cmd[10] = Sh3607601x.rom[0].sct;
        //         temp = User_App_Conversion_Occv(Sh3607601x.rom[0].occv);
        //         cmd[11] = temp >> 8;
        //         cmd[12] = temp;
        //         cmd[13] = Sh3607601x.rom[0].occt;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 读rom温度保护 */
        //     case 0x22:
        //     {
        //         u8 cmd[13];
        //         cmd[0]  = 0xAA;
        //         cmd[1]  = data[1];
        //         cmd[2]  = 0x09;
        //         cmd[12] = 0xFF;
        //         cmd[3] = calculate_temp_from_adc(Sh3607601x.rom[0].otc);
        //         cmd[4] = calculate_temp_from_adc(Sh3607601x.rom[0].otcr);
        //         cmd[5] = calculate_rt_from_reg(  Sh3607601x.rom[0].utc);
        //         cmd[6] = calculate_rt_from_reg(  Sh3607601x.rom[0].utcr);
        //         cmd[7] = calculate_temp_from_adc(Sh3607601x.rom[0].otd);
        //         cmd[8] = calculate_temp_from_adc(Sh3607601x.rom[0].otdr);
        //         cmd[9] = calculate_rt_from_reg(  Sh3607601x.rom[0].utd);
        //         cmd[10] = calculate_rt_from_reg( Sh3607601x.rom[0].utdr);
        //         cmd[11] = User_App_Conversion_Tc(Sh3607601x.rom[0].tc);
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     /* 一键唤醒 */
        //     case 0x23:      
        //     {
        //         u8 cmd[5] = {0};
        //         cmd[0] = 0xAA;
        //         cmd[1] = data[1];
        //         cmd[2] = 0x00;
        //         cmd[3] = 0x00;
        //         cmd[4] = 0xFF;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         queue_push(&q, QUEUE_RESET1_TASK, QUEUE_RESET1_TIME);
        //         queue_push(&q, QUEUE_RESET2_TASK, QUEUE_RESET2_TIME);
        //         break;
        //     }
        //     /* 读取告警信息 */
        //     case 0x24:
        //     {
        //         u8 cmd[8] = {0};
        //         cmd[0] = 0xAA;
        //         cmd[1] = data[1];
        //         cmd[2] = 0x04;
        //         cmd[3] = System.Alarm.alarm_info_comparison >> 24;
        //         cmd[4] = System.Alarm.alarm_info_comparison >> 16;
        //         cmd[5] = System.Alarm.alarm_info_comparison >> 8;
        //         cmd[6] = System.Alarm.alarm_info_comparison;
        //         cmd[7] = 0xFF;
        //         blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
        //         break;
        //     }
        //     default: break;
        // }
    }

    /* 读取实时信息 */
    if (0x7e == data[0] && data[2] <= 0x0A && len >= 4)
    {
    //     switch (data[2])
    //     {
    //         /* BMS实时信息 */
    //         case 0x01:
    //         {
    //             user_app_ble_read_bmsInfo();
    //             break;
    //         }
    //         /* 单体电压 */
    //         case 0x02:
    //         {
    //             user_app_ble_read_cellVoltage();
    //             break;
    //         }
    //         /* 单点温度 */
    //         case 0x03:
    //         {
    //             user_app_ble_read_singlePointTemperature();
    //             break;
    //         }
    //         /* 平衡状态（未用到） */
    //         case 0x04:
    //         {
    //             user_app_ble_read_equilibriumState();
    //             break;
    //         }
    //         /* 上位机广播（未用到） */
    //         case 0x05:
    //         {
    //             user_app_ble_read_broadcast();
    //             break;
    //         }
    //         /* 合并信息（未用到） */
    //         case 0x0A:
    //         {
    //             user_app_ble_read_mergeInfo();
    //             break;
    //         }
    //         default:
    //             break;
    //     }
    }
    /* 读取系统信息 */
    else if (data[2] <= 0x30)
    {
    //     switch (data[2])
    //     {
    //         /* 读取产品信息 */
    //         case 0x20:
    //         {
    //             user_app_ble_read_productsInfo();
    //             break;
    //         }
    //         /* 读取产品序列号 */
    //         case 0x21:
    //         {
    //             user_app_ble_read_productsSerialNumber();
    //             break;
    //         }
    //         /* 读取BMS时钟（未用到） */
    //         case 0x22:
    //         {
    //             user_app_ble_read_bmsRTC();
    //             break;
    //         }
    //         /* 读取告警参数（未用到） */
    //         case 0x23:
    //         {
    //             user_app_ble_read_alarmParameters();
    //             break;
    //         }
    //         /* 读取生产信息 */
    //         case 0x24:
    //         {
    //             user_app_ble_read_productionInfo();
    //             break;
    //         }
    //         /* 合并信息 （未用到）*/
    //         case 0x30:
    //         {
    //             user_app_ble_read_mergeInfo();
    //             break;
    //         }
    //         default:
    //             break;
    //     }
    }
    /* 设置参数 */
    else if (data[2] <= 0x4F)
    {
    //     switch (data[2])
    //     {
    //         /* 设置工作模式 （未用到）*/
    //         case 0x40:
    //         {
    //             user_app_ble_set_WorkMode();
    //             break;
    //         }
    //         /* 设置产品序列号 */
    //         case 0x41:
    //         {
    //             u8 flg = USER_APP_FLASH_FLG;
    //             memcpy(&System.Version.serial_num[0], &data[4], data[3]);
    //             My_Flash_Write(FLASH_ADDR_SERIAL_NUMBER_FLG, &flg, 1);
    //             My_Flash_Write(FLASH_ADDR_SERIAL_NUMBER, &data[4], data[3]);
    //             user_app_ble_set_productsSerialNumber();
    //             break;
    //         }
    //         /* 设置BMS时钟 （未用到） */
    //         case 0x42:
    //         {
    //             user_app_ble_set_bmsRTC();
    //             break;
    //         }
    //         /* 设置告警参数 （未用到）*/
    //         case 0x43:
    //         {
    //             user_app_ble_set_alarmParameters();
    //             break;
    //         }
    //         /* 设置出厂信息 */
    //         case 0x44:
    //         {
    //             u8 flg = USER_APP_FLASH_FLG;
    //             System.Version.date[0] = (data[4] << 8) | data[5];
    //             System.Version.date[1] = data[6];
    //             System.Version.date[2] = data[7];
    //             System.Version.order_num = (data[8] << 8) | data[9];
    //             My_Flash_Write(FLASH_ADDR_EX_FACTORY_FLG, &flg, 1);
    //             My_Flash_Write(FLASH_ADDR_EX_FACTORY, &data[4], 6);
    //             user_app_ble_set_appearanceInfo();
    //             break;
    //         }
    //         /* 设置调试数据 （未用到）*/
    //         case 0x4F:
    //         {
    //             user_app_ble_set_debuggingData();
    //             break;
    //         }
    //         default:
    //             break;
    //     }
    }
}
/* 串口数据处理 */
void User_App_Sh367601x_Uart_process(unsigned char *data, unsigned char len)
{
    if (0x5A == data[2])
    {
        switch (data[1])
        {
            /* 写使能 */
            case 0x0A:
            {
                printf("write cmd successful\n");
                break;
            }
            /* 写命令 */
            case 0x01:
            {
                printf("write successful\n");
                break;
            }
            /* 读rom */
            case 0x02:
            {
                sh3676010b.parser.parse_rom(&sh3676010b, &data[5]);
                sh3676010b.bms_sync.update_protection_config(&sh3676010b);
                break;
            }
            /* 读ram */
            case 0x03:
            {
                /* 处理电流 */
                if (0x02 == data[4])
                {
                    sh3676010b.bms_sync.process_charge_discharge_data(&sh3676010b);
                    sh3676010b.bms_sync.process_current_data(&sh3676010b, (data[5] << 8) | data[6]);
                }
                else
                {
                    sh3676010b.parser.parse_ram(&sh3676010b, &data[5]);
                    // sh3676010b.parser.print_ram(&sh3676010b);
                    sh3676010b.bms_sync.update_realtime_data(&sh3676010b);
                }
                break;
            }
            /* 软件复位 */
            case 0x0B:
            {
                printf("reset\n");
                break;
            }
            default: break;
        }
    }
}



/* 数据处理 */
void User_App_Logic(void)
{
    /* 处理蓝牙数据 */
    if (Ble.flg)
    {
        Ble.flg = false;
        User_App_Sh367601x_Ble_process(Ble.buff, Ble.len);
    }
    /* 处理串口数据 */
    if (Uart.flg)
    {
        Uart.flg = false;
        User_App_Sh367601x_Uart_process(Uart.buff, Uart.len);
    }
    
    /* 队列 */
    if (clock_time_exceed(read_time,  node.time * 1000))
    {
        /* 解锁 */
        sh367601x_task_lock = false;
        node.time = 0;
        if (!queue_empty(&q))
        {
            /* 加锁 */
            sh367601x_task_lock = true;
            queue_pop(&q, &node);
            switch (node.data)
            {
                case QUEUE_CURR_TASK:
                {
                    // User_App_Rs2058_Gpio1_Low();
                    sh3676010b.comm.read_ram(RAM_CURRENT_ADDR_START, RAM_CURRENT_ADDR_LEN);
                    // sh3676010b.comm.read_ram(RAM_CURRENT_ADDR_START, RAM_CURRENT_ADDR_LEN);
                    break;
                }
                case QUEUE_RAM1_TASK:
                {
                    // User_App_Rs2058_Gpio1_Low();
                    sh3676010b.comm.read_ram(RAM_ADDR_START, RAM_ADDR_LEN);
                    break;
                }
                case QUEUE_RAM2_TASK:
                {
                    // User_App_Rs2058_Gpio1_Hight();
                    // sh3676016b.comm.read_ram(RAM_ADDR_START, RAM_ADDR_LEN);
                    break;
                }
                case QUEUE_ROM1_TASK:
                {
                    // User_App_Rs2058_Gpio1_Low();
                    sh3676010b.comm.read_rom(ROM_ADDR_START, ROM_ADDR_LEN);
                    break;
                }
                case QUEUE_ROM2_TASK:
                {
                    // User_App_Rs2058_Gpio1_Hight();
                    // sh3676016b.comm.read_rom(ROM_ADDR_START, ROM_ADDR_LEN);
                    break;
                }
                case QUEUE_WRITE1_ENABLE_TASK:
                {
                    // User_App_Rs2058_Gpio1_Low();
                    // sleep_ms(10);
                    // User_Sh3607601b_Writecmd();
                    break;
                }
                case QUEUE_WRITE2_ENABLE_TASK:
                {
                    // User_App_Rs2058_Gpio1_Hight();
                    // sleep_ms(10);
                    // User_Sh3607601b_Writecmd();
                    break;
                }
                case QUEUE_WRITE1_01_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_02_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_03_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_04_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_05_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_06_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_07_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_08_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_09_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_0A_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_0B_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_0C_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_0D_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_0E_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_0F_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_10_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_11_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_12_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_13_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_14_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE1_15_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[0][node.data - 7], node.data - 7);
                    break;
                }
                case QUEUE_WRITE2_01_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_02_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_03_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_04_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_05_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_06_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_07_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_08_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_09_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_0A_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_0B_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_0C_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_0D_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_0E_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_0F_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_10_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_11_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_12_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_13_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_14_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                }
                case QUEUE_WRITE2_15_TASK:
                {
                    // User_Sh3607601b_Write(write_rom_data[1][node.data - 28], node.data - 28);
                    break;
                    
                }
                case QUEUE_RESET1_TASK:
                {
                    User_App_Rs2058_Gpio1_Low();
                    sleep_ms(10);
                    // User_Sh3607601b_Reset();
                    break;
                }
                case QUEUE_RESET2_TASK:
                {
                    User_App_Rs2058_Gpio1_Hight();
                    sleep_ms(10);
                    // User_Sh3607601b_Reset();
                    break;
                }
                default: break;
            }
        }
        read_time = clock_time();
    }
}
