/********************************************************************************************************
 * @file    driver_ext.h
 *
 * @brief   This is the header file for B85
 *
 * <AUTHOR> Group
 * @date    May 8,2018
 *
 * @par     Copyright (c) 2018, Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *
 *******************************************************************************************************/
#ifndef DRIVERS_EXT_H_
#define DRIVERS_EXT_H_



#include "ext_misc.h"
#include "mcu_boot.h"
#include "software_uart.h"
#include "mcu_config.h"
#include "rf_pa.h"
#include "ext_calibration.h"


#endif /* DRIVERS_EXT_H_ */
