
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.18363 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeMinGWFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mingw32-make.exe"
    candidate_directories:
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/mingw64/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "/REGISTRY-NOTFOUND/bin/"
      - "c:/MinGW/bin/"
      - "/MinGW/bin/"
      - "/REGISTRY-NOTFOUND/MinGW/bin/"
    searched_directories:
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/mingw32-make.exe.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/mingw32-make.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/mingw32-make.exe.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/mingw32-make.exe"
      - "D:/zip/mingw64/bin/mingw32-make.exe.com"
    found: "D:/zip/mingw64/bin/mingw32-make.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake:63 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:61 (_cmake_find_compiler)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_ASM_COMPILER"
    description: "ASM compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: false
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "D:/zip/mingw64/bin/gcc.exe"
    candidate_directories:
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/mingw64/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
    searched_directories:
      - "D:/zip/mingw64/bin/gcc.exe.com"
    found: "D:/zip/mingw64/bin/gcc.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1290 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Checking whether the ASM compiler is GNU using "--version" matched "(GNU assembler)|(GCC)|(Free Software Foundation)":
      gcc.exe (x86_64-win32-seh-rev0, Built by MinGW-W64 project) 8.1.0
      Copyright (C) 2018 Free Software Foundation, Inc.
      This is free software; see the source for copying conditions.  There is NO
      warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:267 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ar"
    candidate_directories:
      - "D:/zip/mingw64/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
    searched_directories:
      - "D:/zip/mingw64/bin/ar.com"
    found: "D:/zip/mingw64/bin/ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:267 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ranlib"
    candidate_directories:
      - "D:/zip/mingw64/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
    searched_directories:
      - "D:/zip/mingw64/bin/ranlib.com"
    found: "D:/zip/mingw64/bin/ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:267 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "strip"
    candidate_directories:
      - "D:/zip/mingw64/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
    searched_directories:
      - "D:/zip/mingw64/bin/strip.com"
    found: "D:/zip/mingw64/bin/strip.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:267 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld"
    candidate_directories:
      - "D:/zip/mingw64/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
    searched_directories:
      - "D:/zip/mingw64/bin/ld.com"
    found: "D:/zip/mingw64/bin/ld.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:267 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "nm"
    candidate_directories:
      - "D:/zip/mingw64/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
    searched_directories:
      - "D:/zip/mingw64/bin/nm.com"
    found: "D:/zip/mingw64/bin/nm.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:267 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objdump"
    candidate_directories:
      - "D:/zip/mingw64/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
    searched_directories:
      - "D:/zip/mingw64/bin/objdump.com"
    found: "D:/zip/mingw64/bin/objdump.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:267 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objcopy"
    candidate_directories:
      - "D:/zip/mingw64/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
    searched_directories:
      - "D:/zip/mingw64/bin/objcopy.com"
    found: "D:/zip/mingw64/bin/objcopy.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:267 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "readelf"
    candidate_directories:
      - "D:/zip/mingw64/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
    searched_directories:
      - "D:/zip/mingw64/bin/readelf.com"
    found: "D:/zip/mingw64/bin/readelf.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:267 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "dlltool"
    candidate_directories:
      - "D:/zip/mingw64/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
    searched_directories:
      - "D:/zip/mingw64/bin/dlltool.com"
    found: "D:/zip/mingw64/bin/dlltool.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:267 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "addr2line"
    candidate_directories:
      - "D:/zip/mingw64/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
    searched_directories:
      - "D:/zip/mingw64/bin/addr2line.com"
    found: "D:/zip/mingw64/bin/addr2line.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:267 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "D:/zip/mingw64/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
    searched_directories:
      - "D:/zip/mingw64/bin/tapi.com"
      - "D:/zip/mingw64/bin/tapi.exe"
      - "D:/zip/mingw64/bin/tapi"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/tapi.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/tapi.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/tapi"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/tapi.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/tapi.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/tapi"
      - "D:/zip/usr/bin/tapi.com"
      - "D:/zip/usr/bin/tapi.exe"
      - "D:/zip/usr/bin/tapi"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/tapi.com"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/tapi.exe"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/tapi"
      - "C:/Windows/System32/tapi.com"
      - "C:/Windows/System32/tapi.exe"
      - "C:/Windows/System32/tapi"
      - "C:/Windows/tapi.com"
      - "C:/Windows/tapi.exe"
      - "C:/Windows/tapi"
      - "C:/Windows/System32/wbem/tapi.com"
      - "C:/Windows/System32/wbem/tapi.exe"
      - "C:/Windows/System32/wbem/tapi"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi"
      - "D:/Program Files/Git/cmd/tapi.com"
      - "D:/Program Files/Git/cmd/tapi.exe"
      - "D:/Program Files/Git/cmd/tapi"
      - "C:/Program Files/CMake/bin/tapi.com"
      - "C:/Program Files/CMake/bin/tapi.exe"
      - "C:/Program Files/CMake/bin/tapi"
      - "C:/Program Files/dotnet/tapi.com"
      - "C:/Program Files/dotnet/tapi.exe"
      - "C:/Program Files/dotnet/tapi"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi.com"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/tapi.com"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/tapi.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/tapi"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:268 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_ASM_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-"
      - "gcc-ar-"
      - "gcc-ar"
      - "gcc-ar"
    candidate_directories:
      - "D:/zip/mingw64/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
    searched_directories:
      - "D:/zip/mingw64/bin/gcc-ar-.com"
      - "D:/zip/mingw64/bin/gcc-ar-.exe"
      - "D:/zip/mingw64/bin/gcc-ar-"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ar-.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ar-.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ar-"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ar-.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ar-.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ar-"
      - "D:/zip/usr/bin/gcc-ar-.com"
      - "D:/zip/usr/bin/gcc-ar-.exe"
      - "D:/zip/usr/bin/gcc-ar-"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ar-.com"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ar-.exe"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ar-"
      - "C:/Windows/System32/gcc-ar-.com"
      - "C:/Windows/System32/gcc-ar-.exe"
      - "C:/Windows/System32/gcc-ar-"
      - "C:/Windows/gcc-ar-.com"
      - "C:/Windows/gcc-ar-.exe"
      - "C:/Windows/gcc-ar-"
      - "C:/Windows/System32/wbem/gcc-ar-.com"
      - "C:/Windows/System32/wbem/gcc-ar-.exe"
      - "C:/Windows/System32/wbem/gcc-ar-"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-"
      - "D:/Program Files/Git/cmd/gcc-ar-.com"
      - "D:/Program Files/Git/cmd/gcc-ar-.exe"
      - "D:/Program Files/Git/cmd/gcc-ar-"
      - "C:/Program Files/CMake/bin/gcc-ar-.com"
      - "C:/Program Files/CMake/bin/gcc-ar-.exe"
      - "C:/Program Files/CMake/bin/gcc-ar-"
      - "C:/Program Files/dotnet/gcc-ar-.com"
      - "C:/Program Files/dotnet/gcc-ar-.exe"
      - "C:/Program Files/dotnet/gcc-ar-"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-.com"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-.com"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-"
      - "D:/zip/mingw64/bin/gcc-ar-.com"
      - "D:/zip/mingw64/bin/gcc-ar-.exe"
      - "D:/zip/mingw64/bin/gcc-ar-"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ar-.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ar-.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ar-"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ar-.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ar-.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ar-"
      - "D:/zip/usr/bin/gcc-ar-.com"
      - "D:/zip/usr/bin/gcc-ar-.exe"
      - "D:/zip/usr/bin/gcc-ar-"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ar-.com"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ar-.exe"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ar-"
      - "C:/Windows/System32/gcc-ar-.com"
      - "C:/Windows/System32/gcc-ar-.exe"
      - "C:/Windows/System32/gcc-ar-"
      - "C:/Windows/gcc-ar-.com"
      - "C:/Windows/gcc-ar-.exe"
      - "C:/Windows/gcc-ar-"
      - "C:/Windows/System32/wbem/gcc-ar-.com"
      - "C:/Windows/System32/wbem/gcc-ar-.exe"
      - "C:/Windows/System32/wbem/gcc-ar-"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-"
      - "D:/Program Files/Git/cmd/gcc-ar-.com"
      - "D:/Program Files/Git/cmd/gcc-ar-.exe"
      - "D:/Program Files/Git/cmd/gcc-ar-"
      - "C:/Program Files/CMake/bin/gcc-ar-.com"
      - "C:/Program Files/CMake/bin/gcc-ar-.exe"
      - "C:/Program Files/CMake/bin/gcc-ar-"
      - "C:/Program Files/dotnet/gcc-ar-.com"
      - "C:/Program Files/dotnet/gcc-ar-.exe"
      - "C:/Program Files/dotnet/gcc-ar-"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-.com"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-.com"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-"
      - "D:/zip/mingw64/bin/gcc-ar.com"
    found: "D:/zip/mingw64/bin/gcc-ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:268 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_ASM_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-"
      - "gcc-ranlib-"
      - "gcc-ranlib"
      - "gcc-ranlib"
    candidate_directories:
      - "D:/zip/mingw64/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
    searched_directories:
      - "D:/zip/mingw64/bin/gcc-ranlib-.com"
      - "D:/zip/mingw64/bin/gcc-ranlib-.exe"
      - "D:/zip/mingw64/bin/gcc-ranlib-"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ranlib-.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ranlib-.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ranlib-"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ranlib-.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ranlib-.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ranlib-"
      - "D:/zip/usr/bin/gcc-ranlib-.com"
      - "D:/zip/usr/bin/gcc-ranlib-.exe"
      - "D:/zip/usr/bin/gcc-ranlib-"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ranlib-.com"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ranlib-.exe"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ranlib-"
      - "C:/Windows/System32/gcc-ranlib-.com"
      - "C:/Windows/System32/gcc-ranlib-.exe"
      - "C:/Windows/System32/gcc-ranlib-"
      - "C:/Windows/gcc-ranlib-.com"
      - "C:/Windows/gcc-ranlib-.exe"
      - "C:/Windows/gcc-ranlib-"
      - "C:/Windows/System32/wbem/gcc-ranlib-.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-"
      - "D:/Program Files/Git/cmd/gcc-ranlib-.com"
      - "D:/Program Files/Git/cmd/gcc-ranlib-.exe"
      - "D:/Program Files/Git/cmd/gcc-ranlib-"
      - "C:/Program Files/CMake/bin/gcc-ranlib-.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib-.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib-"
      - "C:/Program Files/dotnet/gcc-ranlib-.com"
      - "C:/Program Files/dotnet/gcc-ranlib-.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-.com"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-.com"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-"
      - "D:/zip/mingw64/bin/gcc-ranlib-.com"
      - "D:/zip/mingw64/bin/gcc-ranlib-.exe"
      - "D:/zip/mingw64/bin/gcc-ranlib-"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ranlib-.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ranlib-.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ranlib-"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ranlib-.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ranlib-.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ranlib-"
      - "D:/zip/usr/bin/gcc-ranlib-.com"
      - "D:/zip/usr/bin/gcc-ranlib-.exe"
      - "D:/zip/usr/bin/gcc-ranlib-"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ranlib-.com"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ranlib-.exe"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ranlib-"
      - "C:/Windows/System32/gcc-ranlib-.com"
      - "C:/Windows/System32/gcc-ranlib-.exe"
      - "C:/Windows/System32/gcc-ranlib-"
      - "C:/Windows/gcc-ranlib-.com"
      - "C:/Windows/gcc-ranlib-.exe"
      - "C:/Windows/gcc-ranlib-"
      - "C:/Windows/System32/wbem/gcc-ranlib-.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-"
      - "D:/Program Files/Git/cmd/gcc-ranlib-.com"
      - "D:/Program Files/Git/cmd/gcc-ranlib-.exe"
      - "D:/Program Files/Git/cmd/gcc-ranlib-"
      - "C:/Program Files/CMake/bin/gcc-ranlib-.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib-.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib-"
      - "C:/Program Files/dotnet/gcc-ranlib-.com"
      - "C:/Program Files/dotnet/gcc-ranlib-.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-.com"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-.com"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-"
      - "D:/zip/mingw64/bin/gcc-ranlib.com"
    found: "D:/zip/mingw64/bin/gcc-ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: D:/zip/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is GNU, found in:
        D:/Telink_Project/FN_Project/cmake_build/CMakeFiles/4.1.0-rc1/CompilerIdC/a.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-8.1"
      - "gcc-ar-8"
      - "gcc-ar8"
      - "gcc-ar"
    candidate_directories:
      - "D:/zip/mingw64/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
    searched_directories:
      - "D:/zip/mingw64/bin/gcc-ar-8.1.com"
      - "D:/zip/mingw64/bin/gcc-ar-8.1.exe"
      - "D:/zip/mingw64/bin/gcc-ar-8.1"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ar-8.1.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ar-8.1.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ar-8.1"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ar-8.1.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ar-8.1.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ar-8.1"
      - "D:/zip/usr/bin/gcc-ar-8.1.com"
      - "D:/zip/usr/bin/gcc-ar-8.1.exe"
      - "D:/zip/usr/bin/gcc-ar-8.1"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ar-8.1.com"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ar-8.1.exe"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ar-8.1"
      - "C:/Windows/System32/gcc-ar-8.1.com"
      - "C:/Windows/System32/gcc-ar-8.1.exe"
      - "C:/Windows/System32/gcc-ar-8.1"
      - "C:/Windows/gcc-ar-8.1.com"
      - "C:/Windows/gcc-ar-8.1.exe"
      - "C:/Windows/gcc-ar-8.1"
      - "C:/Windows/System32/wbem/gcc-ar-8.1.com"
      - "C:/Windows/System32/wbem/gcc-ar-8.1.exe"
      - "C:/Windows/System32/wbem/gcc-ar-8.1"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8.1.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8.1.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8.1"
      - "D:/Program Files/Git/cmd/gcc-ar-8.1.com"
      - "D:/Program Files/Git/cmd/gcc-ar-8.1.exe"
      - "D:/Program Files/Git/cmd/gcc-ar-8.1"
      - "C:/Program Files/CMake/bin/gcc-ar-8.1.com"
      - "C:/Program Files/CMake/bin/gcc-ar-8.1.exe"
      - "C:/Program Files/CMake/bin/gcc-ar-8.1"
      - "C:/Program Files/dotnet/gcc-ar-8.1.com"
      - "C:/Program Files/dotnet/gcc-ar-8.1.exe"
      - "C:/Program Files/dotnet/gcc-ar-8.1"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8.1.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8.1.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8.1"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-8.1.com"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-8.1.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-8.1"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8.1.com"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8.1.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8.1"
      - "D:/zip/mingw64/bin/gcc-ar-8.com"
      - "D:/zip/mingw64/bin/gcc-ar-8.exe"
      - "D:/zip/mingw64/bin/gcc-ar-8"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ar-8.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ar-8.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ar-8"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ar-8.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ar-8.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ar-8"
      - "D:/zip/usr/bin/gcc-ar-8.com"
      - "D:/zip/usr/bin/gcc-ar-8.exe"
      - "D:/zip/usr/bin/gcc-ar-8"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ar-8.com"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ar-8.exe"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ar-8"
      - "C:/Windows/System32/gcc-ar-8.com"
      - "C:/Windows/System32/gcc-ar-8.exe"
      - "C:/Windows/System32/gcc-ar-8"
      - "C:/Windows/gcc-ar-8.com"
      - "C:/Windows/gcc-ar-8.exe"
      - "C:/Windows/gcc-ar-8"
      - "C:/Windows/System32/wbem/gcc-ar-8.com"
      - "C:/Windows/System32/wbem/gcc-ar-8.exe"
      - "C:/Windows/System32/wbem/gcc-ar-8"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8"
      - "D:/Program Files/Git/cmd/gcc-ar-8.com"
      - "D:/Program Files/Git/cmd/gcc-ar-8.exe"
      - "D:/Program Files/Git/cmd/gcc-ar-8"
      - "C:/Program Files/CMake/bin/gcc-ar-8.com"
      - "C:/Program Files/CMake/bin/gcc-ar-8.exe"
      - "C:/Program Files/CMake/bin/gcc-ar-8"
      - "C:/Program Files/dotnet/gcc-ar-8.com"
      - "C:/Program Files/dotnet/gcc-ar-8.exe"
      - "C:/Program Files/dotnet/gcc-ar-8"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-8.com"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-8.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-8"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8.com"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8"
      - "D:/zip/mingw64/bin/gcc-ar8.com"
      - "D:/zip/mingw64/bin/gcc-ar8.exe"
      - "D:/zip/mingw64/bin/gcc-ar8"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ar8.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ar8.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ar8"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ar8.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ar8.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ar8"
      - "D:/zip/usr/bin/gcc-ar8.com"
      - "D:/zip/usr/bin/gcc-ar8.exe"
      - "D:/zip/usr/bin/gcc-ar8"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ar8.com"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ar8.exe"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ar8"
      - "C:/Windows/System32/gcc-ar8.com"
      - "C:/Windows/System32/gcc-ar8.exe"
      - "C:/Windows/System32/gcc-ar8"
      - "C:/Windows/gcc-ar8.com"
      - "C:/Windows/gcc-ar8.exe"
      - "C:/Windows/gcc-ar8"
      - "C:/Windows/System32/wbem/gcc-ar8.com"
      - "C:/Windows/System32/wbem/gcc-ar8.exe"
      - "C:/Windows/System32/wbem/gcc-ar8"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar8.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar8.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar8"
      - "D:/Program Files/Git/cmd/gcc-ar8.com"
      - "D:/Program Files/Git/cmd/gcc-ar8.exe"
      - "D:/Program Files/Git/cmd/gcc-ar8"
      - "C:/Program Files/CMake/bin/gcc-ar8.com"
      - "C:/Program Files/CMake/bin/gcc-ar8.exe"
      - "C:/Program Files/CMake/bin/gcc-ar8"
      - "C:/Program Files/dotnet/gcc-ar8.com"
      - "C:/Program Files/dotnet/gcc-ar8.exe"
      - "C:/Program Files/dotnet/gcc-ar8"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar8.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar8.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar8"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar8.com"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar8.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar8"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar8.com"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar8.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar8"
      - "D:/zip/mingw64/bin/gcc-ar.com"
    found: "D:/zip/mingw64/bin/gcc-ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-8.1"
      - "gcc-ranlib-8"
      - "gcc-ranlib8"
      - "gcc-ranlib"
    candidate_directories:
      - "D:/zip/mingw64/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
    searched_directories:
      - "D:/zip/mingw64/bin/gcc-ranlib-8.1.com"
      - "D:/zip/mingw64/bin/gcc-ranlib-8.1.exe"
      - "D:/zip/mingw64/bin/gcc-ranlib-8.1"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ranlib-8.1.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ranlib-8.1.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ranlib-8.1"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ranlib-8.1.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ranlib-8.1.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ranlib-8.1"
      - "D:/zip/usr/bin/gcc-ranlib-8.1.com"
      - "D:/zip/usr/bin/gcc-ranlib-8.1.exe"
      - "D:/zip/usr/bin/gcc-ranlib-8.1"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ranlib-8.1.com"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ranlib-8.1.exe"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ranlib-8.1"
      - "C:/Windows/System32/gcc-ranlib-8.1.com"
      - "C:/Windows/System32/gcc-ranlib-8.1.exe"
      - "C:/Windows/System32/gcc-ranlib-8.1"
      - "C:/Windows/gcc-ranlib-8.1.com"
      - "C:/Windows/gcc-ranlib-8.1.exe"
      - "C:/Windows/gcc-ranlib-8.1"
      - "C:/Windows/System32/wbem/gcc-ranlib-8.1.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-8.1.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-8.1"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8.1.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8.1.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8.1"
      - "D:/Program Files/Git/cmd/gcc-ranlib-8.1.com"
      - "D:/Program Files/Git/cmd/gcc-ranlib-8.1.exe"
      - "D:/Program Files/Git/cmd/gcc-ranlib-8.1"
      - "C:/Program Files/CMake/bin/gcc-ranlib-8.1.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib-8.1.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib-8.1"
      - "C:/Program Files/dotnet/gcc-ranlib-8.1.com"
      - "C:/Program Files/dotnet/gcc-ranlib-8.1.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-8.1"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8.1.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8.1.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8.1"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-8.1.com"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-8.1.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-8.1"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8.1.com"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8.1.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8.1"
      - "D:/zip/mingw64/bin/gcc-ranlib-8.com"
      - "D:/zip/mingw64/bin/gcc-ranlib-8.exe"
      - "D:/zip/mingw64/bin/gcc-ranlib-8"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ranlib-8.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ranlib-8.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ranlib-8"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ranlib-8.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ranlib-8.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ranlib-8"
      - "D:/zip/usr/bin/gcc-ranlib-8.com"
      - "D:/zip/usr/bin/gcc-ranlib-8.exe"
      - "D:/zip/usr/bin/gcc-ranlib-8"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ranlib-8.com"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ranlib-8.exe"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ranlib-8"
      - "C:/Windows/System32/gcc-ranlib-8.com"
      - "C:/Windows/System32/gcc-ranlib-8.exe"
      - "C:/Windows/System32/gcc-ranlib-8"
      - "C:/Windows/gcc-ranlib-8.com"
      - "C:/Windows/gcc-ranlib-8.exe"
      - "C:/Windows/gcc-ranlib-8"
      - "C:/Windows/System32/wbem/gcc-ranlib-8.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-8.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-8"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8"
      - "D:/Program Files/Git/cmd/gcc-ranlib-8.com"
      - "D:/Program Files/Git/cmd/gcc-ranlib-8.exe"
      - "D:/Program Files/Git/cmd/gcc-ranlib-8"
      - "C:/Program Files/CMake/bin/gcc-ranlib-8.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib-8.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib-8"
      - "C:/Program Files/dotnet/gcc-ranlib-8.com"
      - "C:/Program Files/dotnet/gcc-ranlib-8.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-8"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-8.com"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-8.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-8"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8.com"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8"
      - "D:/zip/mingw64/bin/gcc-ranlib8.com"
      - "D:/zip/mingw64/bin/gcc-ranlib8.exe"
      - "D:/zip/mingw64/bin/gcc-ranlib8"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ranlib8.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ranlib8.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/gcc-ranlib8"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ranlib8.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ranlib8.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/gcc-ranlib8"
      - "D:/zip/usr/bin/gcc-ranlib8.com"
      - "D:/zip/usr/bin/gcc-ranlib8.exe"
      - "D:/zip/usr/bin/gcc-ranlib8"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ranlib8.com"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ranlib8.exe"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/gcc-ranlib8"
      - "C:/Windows/System32/gcc-ranlib8.com"
      - "C:/Windows/System32/gcc-ranlib8.exe"
      - "C:/Windows/System32/gcc-ranlib8"
      - "C:/Windows/gcc-ranlib8.com"
      - "C:/Windows/gcc-ranlib8.exe"
      - "C:/Windows/gcc-ranlib8"
      - "C:/Windows/System32/wbem/gcc-ranlib8.com"
      - "C:/Windows/System32/wbem/gcc-ranlib8.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib8"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib8.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib8.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib8"
      - "D:/Program Files/Git/cmd/gcc-ranlib8.com"
      - "D:/Program Files/Git/cmd/gcc-ranlib8.exe"
      - "D:/Program Files/Git/cmd/gcc-ranlib8"
      - "C:/Program Files/CMake/bin/gcc-ranlib8.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib8.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib8"
      - "C:/Program Files/dotnet/gcc-ranlib8.com"
      - "C:/Program Files/dotnet/gcc-ranlib8.exe"
      - "C:/Program Files/dotnet/gcc-ranlib8"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib8.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib8.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib8"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib8.com"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib8.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib8"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib8.com"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib8.exe"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib8"
      - "D:/zip/mingw64/bin/gcc-ranlib.com"
    found: "D:/zip/mingw64/bin/gcc-ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU.cmake:167 (enable_language)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU-ASM.cmake:2 (__windows_compiler_gnu)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeASMInformation.cmake:38 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "windres"
      - "windres"
    candidate_directories:
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/"
      - "D:/zip/mingw64/bin/"
      - "D:/zip/usr/bin/"
      - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/Program Files/Git/cmd/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/FN_Project/bin/"
      - "C:/Program Files (x86)/FN_Project/sbin/"
      - "C:/Program Files (x86)/FN_Project/"
    searched_directories:
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/windres.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/windres.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/tc32/bin/windres"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/windres.com"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/windres.exe"
      - "C:/Users/<USER>/.Telink_Tools/tc32_130_Windows/bin/windres"
      - "D:/zip/mingw64/bin/windres.com"
    found: "D:/zip/mingw64/bin/windres.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin"
        - "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin"
        - "D:/zip/mingw64/bin"
        - "D:/zip/mingw64/bin"
        - "D:\\zip\\mingw64\\bin"
        - "D:\\zip\\usr\\bin"
        - "D:/Program Files (x86)/VMware/VMware Workstation/bin/"
        - "C:/Windows/system32"
        - "C:/Windows"
        - "C:/Windows/System32/Wbem"
        - "C:/Windows/System32/WindowsPowerShell/v1.0/"
        - "D:/Program Files/Git/cmd"
        - "C:/Program Files/CMake/bin"
        - "C:/Program Files/dotnet/"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "D:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "D:/zip/mingw64/bin"
        - "D:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/FN_Project"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/FN_Project"
...
