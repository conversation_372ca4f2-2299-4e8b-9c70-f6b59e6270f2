// #include "user_app_main.h"

// unsigned short checksum(unsigned char *data, unsigned char len)
// {
//     unsigned short cs = 0;
//     for (int i = 0; i < len; i++)
//     {
//         cs ^= data[i];
//         for (int j = 0; j < 8; j++)
//         {
//             if ((cs & 1) == 1)
//             {
//                 cs >>= 1;
//                 cs ^= 0xa001;
//             }
//             else
//             {
//                 cs >>= 1;
//             }
//         }
//     }
//     return cs;
// }

// /* 读取bms实时信息 */
// void user_app_ble_read_bmsInfo(void)
// {
//     unsigned char cmd[64] = {0}, soc = System.Soc.soc, soh = System.Soh.soh, batt_state = System.Current.direction;
//     unsigned short check = 0;
//     unsigned int total_voltage = System.Voltage.voltage, max_voltage = System.Voltage.max_voltage, min_voltage = System.Voltage.min_voltage,
//     current = System.Current.current, max_charging_current = System.Current.max_charging_current, max_discharge_current = System.Current.max_discharge_current;

//     cmd[0] = 0x7E;
//     cmd[1] = 0x01;
//     cmd[2] = 0x01;
//     cmd[3] = 0x2F;
//     /* 总电压 */
//     cmd[4] = total_voltage >> 24;
//     cmd[5] = total_voltage >> 16;
//     cmd[6] = total_voltage >> 8;
//     cmd[7] = total_voltage;
//     /* 电流 */
//     cmd[8] =  current >> 24;
//     cmd[9] =  current >> 16;
//     cmd[10] = current >> 8;
//     cmd[11] = current;
//     /* 电池健康度、电量 */
//     cmd[12] = soh;
//     cmd[13] = (unsigned char)soc;
//     /* 剩余容量、设计容量 */
//     cmd[14] = 0x00;
//     cmd[15] = 0x00;
//     cmd[16] = 0x00;
//     cmd[17] = 0x00;
//     /* 最大电压、最小电压 */
//     cmd[18] = max_voltage >> 8;
//     cmd[19] = max_voltage;
//     cmd[20] = min_voltage >> 8;
//     cmd[21] = min_voltage;
//     /* 最高电芯单点温度
//     最低电芯单点温度
//     最高MOS单点温度
//     最高均衡单点温度
//     最高环境单点温度
//     最低环境单点温度 */
//     cmd[22] = 0x00;
//     cmd[23] = 0x00;
//     cmd[24] = 0x00;
//     cmd[25] = 0x00;
//     cmd[26] = 0x00;
//     cmd[27] = 0x00;
//     /* 电池状态 */
//     cmd[28] = batt_state;
//     /* mos管状态、告警信息、系统状态 */
//     cmd[29] = 0x00;
//     cmd[30] = 0x00;
//     cmd[31] = 0x00;
//     cmd[32] = 0x00;
//     cmd[33] = 0x00;
//     cmd[34] = 0x00;
//     cmd[35] = 0x00;
//     cmd[36] = 0x00;
//     cmd[37] = 0x00;
//     cmd[38] = 0x00;
//     cmd[39] = 0x00;
//     cmd[40] = 0x00;
//     cmd[41] = 0x00;
//     cmd[42] = 0x00;
//     cmd[43] = 0x00;
//     cmd[44] = 0x00;
//     /* 循环次数、满充容量 */
//     cmd[45] = 0x00;
//     cmd[46] = 0x00;
//     cmd[47] = 0x00;
//     cmd[48] = 0x00;
//     cmd[49] = 0x00;
//     cmd[50] = 0x00;
//     cmd[51] = 0x00;
//     cmd[52] = 0x00;
//     /* 最大充电、放电电流 */
//     cmd[53] = max_charging_current >> 24;
//     cmd[54] = max_charging_current >> 16;
//     cmd[55] = max_charging_current >> 8;
//     cmd[56] = max_charging_current;
//     cmd[57] = max_discharge_current >> 24;
//     cmd[58] = max_discharge_current >> 16;
//     cmd[59] = max_discharge_current >> 8;
//     cmd[60] = max_discharge_current;

//     /* 校验 */
//     check = checksum(&cmd[1], 60);
//     cmd[61] = check >> 8;
//     cmd[62] = check;
//     cmd[63] = 0x0D;
//     blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 读取单体电压 */
// void user_app_ble_read_cellVoltage(void)
// {
//     unsigned char cmd[80] = {0}, batt_num = System.Voltage.num[0] + System.Voltage.num[1];
//     unsigned short check;
//     cmd[0] = 0x7E;
//     cmd[1] = 0x01;
//     cmd[2] = 0x02;
//     cmd[3] = batt_num * 2 + 2;
//     cmd[4] = 0x00;
//     cmd[5] = batt_num;
//     for (unsigned short i = 0; i < batt_num; i++)
//     {
//         cmd[6 + (i * 2)] = System.Voltage.single_voltage[i] >> 8;
//         cmd[7 + (i * 2)] = System.Voltage.single_voltage[i];
//     }
//     check = checksum(&cmd[1], 6 + (batt_num * 2));
//     cmd[7 + (batt_num * 2)] = check >> 8;
//     cmd[8 + (batt_num * 2)] = check;
//     cmd[9 + (batt_num * 2)] = 0x0D;
//     blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, 10 + (batt_num * 2));
// }
// /* 单点温度 */
// void user_app_ble_read_singlePointTemperature(void)
// {
//     unsigned char cmd[17] = {0};
//     unsigned short check;
//     cmd[0] = 0x7E;
//     cmd[1] = 0x01;
//     cmd[2] = 0x03;
//     cmd[3] = 0x07;
//     cmd[4] = 0x00;
//     cmd[5] = 0x00;
//     cmd[6] = 0x00;
//     /* 外部温度 */
//     cmd[7] = 0x06;
//     cmd[8] =  System.Temp.outer_temp[0];
//     cmd[9] =  System.Temp.outer_temp[1];
//     cmd[10] = System.Temp.outer_temp[2];
//     cmd[11] = System.Temp.outer_temp[3];
//     cmd[12] = System.Temp.outer_temp[4];
//     cmd[13] = System.Temp.outer_temp[5];
//     check = checksum(&cmd[1], 13);
//     cmd[14] = check >> 8;
//     cmd[15] = check;
//     cmd[16] = 0x0D;
//     blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 读取平衡状态（暂无用到） */
// void user_app_ble_read_equilibriumState(void)
// {
//     unsigned char cmd[12] = {0};
//     unsigned short check;
//     cmd[0] = 0x7E;
//     cmd[1] = 0x01;
//     cmd[2] = 0x04;
//     cmd[3] = 0x05;
//     cmd[4] = 0x00;
//     cmd[5] = 0x00;
//     cmd[6] = 0x00;
//     cmd[7] = 0x00;
//     cmd[8] = 0x00;
//     check = checksum(&cmd[1], 8);
//     cmd[9] = check >> 8;
//     cmd[10] = check;
//     cmd[11] = 0x0D;
//     blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 读取上位机广播（暂无用到） */
// void user_app_ble_read_broadcast(void)
// {
//     // unsigned char cmd[5] = {0};
//     // cmd[0] = 0x7E;
//     // cmd[1] = 0x01;
//     // cmd[2] = 0x05;
//     // cmd[3] = 0x00;
//     // cmd[4] = 0x0D;
//     // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 读取合并信息（暂无用到） */
// void user_app_ble_read_mergeInfo(void)
// {
//     // unsigned char cmd[5] = {0};
//     // cmd[0] = 0x7E;
//     // cmd[1] = 0x01;
//     // cmd[2] = 0x0A;
//     // cmd[3] = 0x00;
//     // cmd[4] = 0x0D;
//     // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 读取产品信息 */
// void user_app_ble_read_productsInfo(void)
// {
//     unsigned char cmd[15] = {0};
//     unsigned short check;
//     cmd[0] = 0x7E;
//     cmd[1] = 0x01;
//     cmd[2] = 0x20;
//     cmd[3] = 0x08;
//     cmd[4] = System.Version.hardware_version[0];
//     cmd[5] = System.Version.hardware_version[1];
//     cmd[6] = System.Version.software_version[0];
//     cmd[7] = System.Version.software_version[1];
//     cmd[8] = 0x00;
//     cmd[9] = 0x00;
//     cmd[10] = 0x00; 
//     cmd[11] = 0x00;
//     check = checksum(&cmd[1], 10);
//     cmd[12] = check >> 8;
//     cmd[13] = check;
//     cmd[14] = 0x0D;
//     blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 读取产品序列号 */
// void user_app_ble_read_productsSerialNumber(void)
// {
//     unsigned char cmd[50] = {0}, len = sizeof(System.Version.serial_num);
//     unsigned short check;
//     cmd[0] = 0x7E;
//     cmd[1] = 0x01;
//     cmd[2] = 0x21;
//     cmd[3] = len;
//     memcpy(&cmd[4], &System.Version.serial_num[0], len);
//     check = checksum(&cmd[1], 4 + cmd[4]);
//     cmd[5 + cmd[4]] = check >> 8;
//     cmd[6 + cmd[4]] = check;
//     cmd[7 + cmd[4]] = 0x0D;
//     blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, 8 + cmd[4]);
// }
// /* 读取BMS时钟（暂无用到） */
// void user_app_ble_read_bmsRTC(void)
// {
//     // unsigned char cmd[13] = {0};
//     // unsigned short check;
//     // cmd[0] = 0x7E;
//     // cmd[1] = 0x01;
//     // cmd[2] = 0x22;
//     // cmd[3] = 0x06;
//     // cmd[4] = 0x00;
//     // cmd[5] = 0x00;
//     // cmd[6] = 0x00;
//     // cmd[7] = 0x00;
//     // cmd[8] = 0x00;
//     // cmd[9] = 0x00;
//     // check = checksum(&cmd[1], 9);
//     // cmd[10] = check >> 8;
//     // cmd[11] = check;
//     // cmd[12] = 0x0D;
//     // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 读取告警参数（暂无用到） */
// void user_app_ble_read_alarmParameters(void)
// {
//     unsigned char cmd[19] = {0};
//     unsigned short check;
//     cmd[0] = 0x7E;
//     cmd[1] = 0x01;
//     cmd[2] = 0x23;
//     cmd[3] = 0x0C;
//     cmd[4] = 0x00;
//     cmd[5] = 0x00;
//     cmd[6] = 0x00;
//     cmd[7] = 0x00;
//     cmd[8] = 0x00;
//     cmd[9] = 0x00;
//     cmd[10] = 0x00;
//     cmd[11] = 0x00;
//     cmd[12] = 0x00;
//     cmd[13] = 0x00;
//     cmd[14] = 0x00;
//     cmd[15] = 0x00;
//     check = checksum(&cmd[1], 15);
//     cmd[16] = check >> 8;
//     cmd[17] = check;
//     cmd[18] = 0x0D;
//     blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 读取生产信息 */
// void user_app_ble_read_productionInfo(void)
// {
//     unsigned char cmd[13] = {0};
//     unsigned short check;
//     cmd[0] = 0x7E;
//     cmd[1] = 0x01;
//     cmd[2] = 0x24;
//     cmd[3] = 0x04;
//     cmd[4] = System.Version.date[0] >> 8;
//     cmd[5] = System.Version.date[0];
//     cmd[6] = System.Version.date[1];
//     cmd[7] = System.Version.date[2];
//     cmd[8] = System.Version.order_num >> 8;
//     cmd[9] = System.Version.order_num;
//     check = checksum(&cmd[1], 9);
//     cmd[10] = check >> 8;
//     cmd[11] = check;
//     cmd[12] = 0x0D;
//     blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 读取合并信息（暂无用到） */
// void user_app_ble_set_mergeInfo(void)
// {
//     // unsigned char cmd[5] = {0};
//     // cmd[0] = 0x7E;
//     // cmd[1] = 0x01;
//     // cmd[2] = 0x30;
//     // cmd[3] = 0x00;
//     // cmd[4] = 0x0D;
//     // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 设置工作模式 （暂无用到）*/
// void user_app_ble_set_WorkMode(void)
// {
//     // unsigned char cmd[5] = {0};
//     // cmd[0] = 0x7E;
//     // cmd[1] = 0x01;
//     // cmd[2] = 0x41;
//     // cmd[3] = 0x00;
//     // cmd[4] = 0x0D;
//     // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 设置产品序列号*/
// void user_app_ble_set_productsSerialNumber(void)
// {
//     unsigned char cmd[9] = {0};
//     cmd[0] = 0x7E;
//     cmd[1] = 0x01;
//     cmd[2] = 0x41;
//     cmd[3] = 0x01;
//     cmd[4] = 0x00;
//     unsigned short check = checksum(&cmd[1], 5);
//     cmd[6] = check >> 8;
//     cmd[7] = check;
//     cmd[8] = 0x0D;
//     blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 设置BMS时钟（暂无用到） */
// void user_app_ble_set_bmsRTC(void)
// {
//     // unsigned char cmd[8] = {0};
//     // cmd[0] = 0x7E;
//     // cmd[1] = 0x01;
//     // cmd[2] = 0x42;
//     // cmd[3] = 0x01;
//     // cmd[4] = 0x00;
//     // unsigned short check = checksum(&cmd[1], 4);
//     // cmd[5] = check >> 8;
//     // cmd[6] = check;
//     // cmd[7] = 0x0D;
//     // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 设置告警参数（暂无用到）*/
// void user_app_ble_set_alarmParameters(void)
// {
//     // unsigned char cmd[8] = {0};
//     // cmd[0] = 0x7E;
//     // cmd[1] = 0x01;
//     // cmd[2] = 0x43;
//     // cmd[3] = 0x01;
//     // cmd[4] = 0x00;
//     // unsigned short check = checksum(&cmd[1], 4);
//     // cmd[5] = check >> 8;
//     // cmd[6] = check;
//     // cmd[7] = 0x0D;
//     // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 设置出厂信息*/
// void user_app_ble_set_appearanceInfo(void)
// {
//     unsigned char cmd[9] = {0};
//     cmd[0] = 0x7E;
//     cmd[1] = 0x01;
//     cmd[2] = 0x44;
//     cmd[3] = 0x01;
//     cmd[4] = 0x00;
//     unsigned short check = checksum(&cmd[1], 5);
//     cmd[6] = check >> 8;
//     cmd[7] = check;
//     cmd[8] = 0x0D;
//     blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 设置调试数据（暂无用到） */
// void user_app_ble_set_debuggingData(void)
// {
//     // unsigned char cmd[5] = {0};
//     // cmd[0] = 0x7E;
//     // cmd[1] = 0x01;
//     // cmd[2] = 0x4F;
//     // cmd[3] = 0x00;
//     // cmd[4] = 0x0D;
//     // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 充放电MOS控制（暂无用到）*/
// void user_app_ble_set_mosCtrl(char value)
// {
//     // unsigned char cmd[5] = {0};
//     // cmd[0] = 0xaa;
//     // cmd[1] = value;
//     // cmd[2] = 0x00;
//     // cmd[3] = 0x00;
//     // cmd[4] = 0xff;
//     // unsigned short check = checksum(&cmd[1], 3);
//     // cmd[4] = check >> 8;
//     // cmd[5] = check;
//     // cmd[6] = 0x0D;
//     // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
// /* 远程控制均衡（暂无用到） */
// void user_app_ble_set_ctrlBalanced(void)
// {
//     // unsigned char cmd[5] = {0};
//     // cmd[0] = 0x7E;
//     // cmd[1] = 0x01;
//     // cmd[2] = 0x51;
//     // cmd[3] = 0x00;
//     // cmd[4] = 0x0D;
//     // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
// }
