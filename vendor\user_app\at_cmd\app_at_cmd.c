/*
 * app_at_cmd.c
 *
 *  Created on: 2024-4-18
 *      Author: lvance
 */

#include "tl_common.h"
#include "drivers.h"
#include "stack/ble/ble.h"
#include "app_at_cmd.h"
#include "../system_main/app_main.h"
#include "../../common/blt_soft_timer.h"
#include "../../b85m_ble_sample/app_config.h"
#include "../../b85m_ble_sample/app_att.h"
#include "../uart/app_usart.h"

#define AT_CMD_NUM_MAX    11
#define AT_CMD_LEN_MAX      15
 const unsigned char atCmdList[AT_INLEGAL_CMD+1][AT_CMD_LEN_MAX] ={
    {"AT\0"},
    {"AT+VER?\0"},
    {"AT+RSTN\0"},
    {"AT+RSTORE\0"},
    {"AT+BRATE\0"},
    {"AT+AUTH\0"},
    {"AT+MAC\0"},
    {"AT+POWER\0"},
    {"AT+ADVNAME\0"},
    {"AT+ADVDATA\0"},
    {"AT+ADVINT\0"},
    {"AT+CONINT\0"}
 };

_attribute_data_retention_ tsAtRemainTime_t atRemainTime;
_attribute_data_retention_ static char atRxBusy=0;
tsAtResponse_t atResponse;

//函数代码
_attribute_ram_code_ int my_strlen(char* x)
{
	int count = 0;
	while(*x !='\0')
	{
		count++;
		x++;
	}
	return count;
}

_attribute_ram_code_ char * my_strstr(const char*str1,const char *str2){


	char *cur =(char *)str1;
	char *s1 = NULL;
	char *s2 = NULL;
	if(!*str2){
		return((char *)str1);
	}
	while(*cur){
		s1=cur;
		s2=(char *)str2;
		while(*s2&&!(*s1-*s2)){
			s1++;
			s2++;
		}
		if(!*s2){
			return cur;
		}
		cur++;
	}

	return NULL;
}

void appAtResponse(u8 *rspData,int rspLen, tsAtSource_t source){
	  if(source == AT_UART){
		  app_usart_fifo_push(rspData,rspLen);
	  }else{
		  bls_att_pushNotifyData(CMD_CLIENT_TO_SERVER_DP_H,rspData,rspLen);
	  }

	  LOG_DEBUG("AT RSP{%s}\r\n",(char *)&rspData);
}

//AT\r\n
void atTest(tsAtSource_t source){
  sprintf((char*)&atResponse.data,"OK\r\n");
  atResponse.length = my_strlen((char*)&atResponse.data);
  appAtResponse(atResponse.data,atResponse.length,source);
}

//AT+VER?\r\n
//+VER:XXXXXX\r\n
void atGetSoftwareVsersion(tsAtSource_t source){
  sprintf((char*)&atResponse.data,"+VER:%s\r\n",(char *)&softwareVer);
  atResponse.length = my_strlen((char*)&atResponse.data);
  appAtResponse(atResponse.data,atResponse.length,source);
}

//AT+RSTN\r\n
//+RSTN:XXXXXX\r\n
void atSoftwareReset(tsAtSource_t source){

  sprintf((char*)&atResponse.data,"+RSTN OK\r\n");
  atResponse.length = my_strlen((char*)&atResponse.data);
  appAtResponse(atResponse.data,atResponse.length,source);
  atRemainTime.cmd |= AT_REMAIN_RSTN;
  atRemainTime.tick[0] = clock_time(); 
}
//AT+RSTORE?\r\n
//+RSTORE:XXXXXX\r\n
void atSoftwareRestore(tsAtSource_t source){
  sprintf((char*)&atResponse.data,"+RSTORE OK\r\n");
  atResponse.length = my_strlen((char*)&atResponse.data);
  appAtResponse(atResponse.data,atResponse.length,source);
  atRemainTime.cmd |= AT_REMAIN_RSTORE;
  atRemainTime.tick[1] = clock_time(); 
}

//AT+BRATE=\r\n
//+BRATE:XXXXXX\r\n
void atSetBrate(u8 *cmd,int cmdLen,tsAtSource_t source){

  if(cmd[8] == '?'){
      sprintf((char*)&atResponse.data,"+BRATE:%d\r\n",paraToFlash.brate);
  }else if(cmd[8] == '='){
    u8 cmdOK=0;
    if (memcmp(&cmd[9],"9600",4) == 0)
    {
      /* code */
      paraToFlash.brate = 9600;
      cmdOK=1;
    }
    else if (memcmp(&cmd[9],"38400",5) == 0)
    {
      /* code */
      paraToFlash.brate = 38400;
      cmdOK=1;
    }
    
    else if (memcmp(&cmd[9],"115200",6) == 0)
    {
      /* code */
      paraToFlash.brate = 115200;
      cmdOK=1;
    }
    else if (memcmp(&cmd[9],"230400",6) == 0)
    {
      /* code */
      paraToFlash.brate = 230400;
      cmdOK=1;
    }
    else
    {
      /* code */
      sprintf((char*)&atResponse.data,"+ERROR(2)\r\n");
    }
    
    if(cmdOK){
      LOG_DEBUG("set brate=%d\r\n",paraToFlash.brate);
      sprintf((char*)&atResponse.data,"+BRATE OK\r\n");
      appSystemParamaterSave();
      atRemainTime.cmd |= AT_REMAIN_BRATE;
      atRemainTime.tick[2] = clock_time(); 
      //blt_soft_timer_add(&app_reinit_uartCB,200*1000);
    }
  }else{
      sprintf((char*)&atResponse.data,"+ERROR(1)\r\n");    
  }
  atResponse.length = my_strlen((char*)&atResponse.data);
  appAtResponse(atResponse.data,atResponse.length,source); 

}

//AT+AUTH=0,123456\r\n
//+AUTH:XXXXXX\r\n
void atAuth(u8 *cmd,int cmdLen,tsAtSource_t source){
  if(cmd[7] == '?'){
      sprintf((char*)&atResponse.data,"+AUTH:%d,%06d\r\n",paraToFlash.authEnable,paraToFlash.authWord);
  }else if(cmd[7] == '='){
    u8 auth = cmd[8] - 0x30;
    if (cmd[9] == ',' && cmd[16]=='\r')
    {
      /* code */
      if (auth<2)
      {
        /* code */
        paraToFlash.authEnable = auth;

        if(paraToFlash.authEnable){
            paraToFlash.authWord = (cmd[10]-0x30)*100000+\
                                   (cmd[11]-0x30)*10000+\
                                   (cmd[12]-0x30)*1000+\
                                   (cmd[13]-0x30)*100+\
                                   (cmd[14]-0x30)*10+\
                                   (cmd[15]-0x30)*1;
			blc_smp_setSecurityLevel(Authenticated_Pairing_with_Encryption);  //if not set, default is : LE_Security_Mode_1_Level_2(Unauthenticated_Pairing_with_Encryption)
			blc_smp_enableAuthMITM(1);
			blc_smp_setBondingMode(Bondable_Mode);	// if not set, default is : Bondable_Mode
			blc_smp_setIoCapability(IO_CAPABILITY_DISPLAY_ONLY);
			blc_smp_peripheral_init();
			blc_smp_configSecurityRequestSending(SecReq_IMM_SEND, SecReq_PEND_SEND, 1000);
        }else{
        	bls_smp_eraseAllPairingInformation();
        	blc_smp_setSecurityLevel(No_Security);

        }

        LOG_DEBUG("auth=%d,%06d\r\n",paraToFlash.authEnable,paraToFlash.authWord);
        appSystemParamaterSave();
        sprintf((char*)&atResponse.data,"+AUTH OK\r\n");                    
      }else{
            /* code */
        sprintf((char*)&atResponse.data,"+ERROR(2)\r\n");
      }
    }else{
            /* code */
      sprintf((char*)&atResponse.data,"+ERROR(1)\r\n");
    }
  }else{
      sprintf((char*)&atResponse.data,"+ERROR(1)\r\n");    
  }
  atResponse.length = my_strlen((char*)&atResponse.data);
  appAtResponse(atResponse.data,atResponse.length,source);  
}
//AT+MAC?\r\n
//+MAC:XXXXXX\r\n
//AT+MAC=A4C138123456\r\n
//+MAC OK\r\n
void atBleMac(u8 *cmd,int cmdLen,tsAtSource_t source){
  if(cmd[6] == '?'){
      u8 blemac[6];
    	flash_read_data(flash_sector_mac_address,sizeof(blemac),(unsigned char *)&blemac);
      sprintf((char*)&atResponse.data,"+MAC:%02X%02X%02X%02X%02X%02X\r\n",blemac[5],blemac[4],blemac[3],blemac[2],blemac[1],blemac[0]);
  }else if(cmd[6] == '='){

    if (cmd[19]=='\r')
    {
      /* code */
        unsigned char mac[6];
        unsigned char macToString[12];

		memcpy(macToString,&cmd[7],12);
		for(unsigned char i=0;i<12;i++){

			if((macToString[i]>='a') && (macToString[i]<='f')){
				macToString[i] = macToString[i]-0x20;
			}
			if(macToString[i]>='0' && macToString[i]<='9'){
				macToString[i]=macToString[i]-0x30;
			}else if(macToString[i]>='A' && macToString[i]<='F'){
				macToString[i]=macToString[i]-0x37;
			}else{
				sprintf((char*)&atResponse.data,"+ERROR(2)\r\n");
				atResponse.length = my_strlen((char*)&atResponse.data);
				appAtResponse(atResponse.data,atResponse.length,source);
				return;
			}
		}
		mac[5] =(macToString[0]<<4) |macToString[1];
		mac[4] =(macToString[2]<<4) |macToString[3];
		mac[3] =(macToString[4]<<4) |macToString[5];
		mac[2] =(macToString[6]<<4) |macToString[7];
		mac[1] =(macToString[8]<<4) |macToString[9];
		mac[0] =(macToString[10]<<4) |macToString[11];
		tlkapi_send_string_data(APP_LOG_EN,"[USER][LOG]set mac",mac,6);
		unsigned char irq = irq_disable();
		flash_erase_sector(CFG_ADR_MAC_512K_FLASH);
		flash_page_program(CFG_ADR_MAC_512K_FLASH,sizeof(mac),(unsigned char *)mac);
		irq_restore(irq);
		atRemainTime.cmd |= AT_REMAIN_RSTN;
		atRemainTime.tick[0] = clock_time();

		sprintf((char*)&atResponse.data,"+MAC OK\r\n");

    }else{
            /* code */
      sprintf((char*)&atResponse.data,"+ERROR(1)\r\n");
    }
  }else{
      sprintf((char*)&atResponse.data,"+ERROR(1)\r\n");    
  }
  atResponse.length = my_strlen((char*)&atResponse.data);
  appAtResponse(atResponse.data,atResponse.length,source);  
}
//AT+POWER?\r\n
//+POWER:XXXXXX\r\n
void atBleTxPower(u8 *cmd,int cmdLen,tsAtSource_t source){
;
}
//AT+ADVNAME?\r\n
//+ADVNAME:XXXXXX\r\n
//AT+ADVNAME=BLE-9527\r\n
//+ADVNAME OK\r\n

void atBleAdvNAME(u8 *cmd,int cmdLen,tsAtSource_t source){
  if(cmd[10] == '?'){
	  sprintf((char*)&atResponse.data,"+ADVNAME:%s\r\n",(char *)&paraToFlash.name);
  }else if(cmd[10] == '='){
	 u8 nameLen =  cmdLen-13;
	if (nameLen <= MAX_ADV_NAME_LEN)
	{
			/* code */
		memset(paraToFlash.name,0,sizeof(paraToFlash.name));
		paraToFlash.nameLen = nameLen;
		memcpy(paraToFlash.name,&cmd[11],paraToFlash.nameLen);


		u8 rspIndex=0;
		extern u8 tbl_scanRsp[31];
		memset(tbl_scanRsp,0,31);
		tbl_scanRsp[rspIndex++]=paraToFlash.nameLen+1;
		tbl_scanRsp[rspIndex++]=0X09;
		memcpy(&tbl_scanRsp[rspIndex],&paraToFlash.name[0],paraToFlash.nameLen);
		rspIndex += paraToFlash.nameLen;
		tbl_scanRsp[rspIndex++] =  0x07;
		tbl_scanRsp[rspIndex++] =  0xff;
		memcpy(&tbl_scanRsp[rspIndex],blemac,6);
		rspIndex += 6;

//		tbl_scanRsp[rspIndex++] = (SW_VER>>8 )& 0x00ff;
//		tbl_scanRsp[rspIndex++] = (SW_VER>>0 )& 0x00ff;
//		bls_ll_setScanRspData( (u8 *)tbl_scanRsp, rspIndex);
		appSystemParamaterSave();
//		bls_ll_setAdvEnable(BLC_ADV_DISABLE);  //ADV enable
//
		bls_att_setDeviceName(paraToFlash.name, paraToFlash.nameLen);
		//my_att_init(); //gatt initialization
		bls_ll_setScanRspData( (u8 *)tbl_scanRsp, sizeof(tbl_scanRsp));
		bls_ll_setAdvEnable(BLC_ADV_ENABLE);  //ADV enable

		sprintf((char*)&atResponse.data,"+ADVNAME OK\r\n");
		tlkapi_send_string_data(APP_LOG_EN,"[USER][LOG] ADV RSP DATA",tbl_scanRsp,rspIndex);

	}else{
			/* code */
	  sprintf((char*)&atResponse.data,"+ERROR(2)\r\n");
	}
  }else{
	  sprintf((char*)&atResponse.data,"+ERROR(1)\r\n");
  }
  atResponse.length = my_strlen((char*)&atResponse.data);
  appAtResponse(atResponse.data,atResponse.length,source);
}

//AT+ADVDATA=1234546789\r\n
//+ADVDATA OK\r\n
void atBleAdvData(u8 *cmd,int cmdLen,tsAtSource_t source){
 if(cmd[10] == '='){
	u8 advLen =  cmdLen -13;
	if (advLen > MAX_ADV_DATA_LEN)
	{
			/* code */
		sprintf((char*)&atResponse.data,"+ERROR(2)\r\n");


	}else{
		memset(paraToFlash.advData,0,sizeof(paraToFlash.advData));
		paraToFlash.advLen = advLen;
		memcpy(paraToFlash.advData,&cmd[11],paraToFlash.advLen);
		appSystemParamaterSave();

		extern u8 advDataIndex;
		extern u8 tbl_advData[];
		advDataIndex=0;
		tbl_advData[advDataIndex++]=0x02;
		tbl_advData[advDataIndex++]=DT_FLAGS;
		tbl_advData[advDataIndex++]=0x06;
		tbl_advData[advDataIndex++]=0x05;
		tbl_advData[advDataIndex++]=DT_INCOMPLETE_LIST_16BIT_SERVICE_UUID;
		tbl_advData[advDataIndex++]=(ADV_SERVICE_TP_16BIT>>0)&0x00ff;
		tbl_advData[advDataIndex++]=(ADV_SERVICE_TP_16BIT>>8)&0x00ff;
		tbl_advData[advDataIndex++]=(ADV_SERVICE_1_16BIT>>0)&0x00ff;
		tbl_advData[advDataIndex++]=(ADV_SERVICE_1_16BIT>>8)&0x00ff;
		if(paraToFlash.advLen){
			tbl_advData[advDataIndex++]=paraToFlash.advLen+1;
			tbl_advData[advDataIndex++]=DATA_TYPE_MANUFACTURER_SPECIFIC_DATA;
			memcpy(&tbl_advData[advDataIndex],paraToFlash.advData,paraToFlash.advLen);
			advDataIndex+=paraToFlash.advLen+1;
		}
		bls_ll_setAdvData( (u8 *)tbl_advData, advDataIndex );



		sprintf((char*)&atResponse.data,"+ADVDATA OK\r\n");
	}
  }else{
	  sprintf((char*)&atResponse.data,"+ERROR(1)\r\n");
  }
  atResponse.length = my_strlen((char*)&atResponse.data);
  appAtResponse(atResponse.data,atResponse.length,source);
}
//AT+ADVINT?\r\n
//+ADVINT:XXXXXX\r\n
//AT+ADVINT=480,500\r\n 10 13 19
//+ADVINT OK\r\n
//32~16448，单位是0.625ms
void atBleAdvInterval(u8 *cmd,int cmdLen,tsAtSource_t source){
  if(cmd[9] == '?'){
	  sprintf((char*)&atResponse.data,"+ADVINT:%d,%d\r\n",paraToFlash.advIntervalMin,paraToFlash.advIntervalMax);
  }else if(cmd[9] == '='){

	 u8 i=10;
	 for(;i<cmdLen;i++){
		 if(cmd[i] == ','){
			 break;
		 }
	 }

	 u8 minL = i-10;
	 u8 maxL = cmdLen-3-i;

	 if((minL <2) || (minL >5) || (maxL<2) || (maxL>5) ||(i>=cmdLen)){
		 sprintf((char*)&atResponse.data,"+ERROR(2)\r\n");
		 LOG_DEBUG("advint parameter length error\r\n");
	 }else{
		 u16 advMin=0;
		 u16 advMax=0;
		 u8 min[5]={0x00};
		 u8 max[5]={0x00};
		 memcpy(min,&cmd[10],minL);
		 memcpy(max,&cmd[i+1],maxL);

		 switch(minL){
		 	 case 2:
		 		advMin = (min[0]-0x30)*10 + min[1]-0x30;
		 		 break;
		 	 case 3:
		 		advMin = (min[0]-0x30)*100 + (min[1]-0x30)*10 + (min[2]-0x30);
		 		 break;
		 	 case 4:
		 		advMin = (min[0]-0x30)*1000 + (min[1]-0x30)*100 + (min[2]-0x30)*10 + (min[3]-0x30);
		 		 break;
		 	 case 5:
		 		advMin = (min[0]-0x30)*10000 + (min[1]-0x30)*1000 + (min[2]-0x30)*100 + (min[3]-0x30)*10 + (min[4]-0x30);
		 		 break;

		 	 default :
		 		 break;
		 }

		 switch(maxL){
		 	 case 2:
		 		advMax = (max[0]-0x30)*10 + max[1]-0x30;
		 		 break;
		 	 case 3:
		 		advMax = (max[0]-0x30)*100 + (max[1]-0x30)*10 + (max[2]-0x30);
		 		 break;
		 	 case 4:
		 		advMax = (max[0]-0x30)*1000 + (max[1]-0x30)*100 + (max[2]-0x30)*10 + (max[3]-0x30);
		 		 break;
		 	 case 5:
		 		advMax = (max[0]-0x30)*10000 + (max[1]-0x30)*1000 + (max[2]-0x30)*100 + (max[3]-0x30)*10 + (max[4]-0x30);
		 		 break;

		 	 default :
		 		 break;
		 }

		 if((advMax<advMin)|| (advMin<32) ||(advMax>16448)){
			sprintf((char*)&atResponse.data,"+ERROR(3)\r\n");
			LOG_DEBUG("advint parameter length error\r\n");
		 }else{
			paraToFlash.advIntervalMin = advMin;
			paraToFlash.advIntervalMax = advMax;
			bls_ll_setAdvEnable(0);
			u8 adv_param_status = bls_ll_setAdvParam(  advMin, advMax,
												 ADV_TYPE_CONNECTABLE_UNDIRECTED, OWN_ADDRESS_PUBLIC,
												 0,  NULL,
												 BLT_ENABLE_ADV_ALL,
												 ADV_FP_NONE);
			bls_ll_setAdvEnable(1);
			if(adv_param_status != BLE_SUCCESS){
				sprintf((char*)&atResponse.data,"+ERROR(4)\r\n");
				LOG_DEBUG("set ADV parameters error 0x%x!!!\n", adv_param_status);
			}else{
				paraToFlash.advIntervalMin = advMin;
				paraToFlash.advIntervalMax = advMax;
				appSystemParamaterSave();
				sprintf((char*)&atResponse.data,"+ADVINT OK\r\n");
				LOG_DEBUG("set ADV parameters ok\r\n");
			}
			 
		 }
	 }

  }else{
	  sprintf((char*)&atResponse.data,"+ERROR(1)\r\n");
  }
  atResponse.length = my_strlen((char*)&atResponse.data);
  appAtResponse(atResponse.data,atResponse.length,source);
}
//AT+CONINT?\r\n
//+CONINT:XXXXXX\r\n
//AT+CONINT=25,40\r\n
//+CONINT OK\r\n
void atBleConnPara(u8 *cmd,int cmdLen,tsAtSource_t source){
	if(cmd[9] == '?'){
	  sprintf((char*)&atResponse.data,"+CONINT:%d,%d\r\n",paraToFlash.conIntervalMin,paraToFlash.conIntervalMax);
	}else if(cmd[9] == '='){

	 u8 i=10;
	 for(;i<cmdLen;i++){
		 if(cmd[i] == ','){
			 break;
		 }
	 }

	 u8 minL = i-10;
	 u8 maxL = cmdLen-3-i;

	 if((minL <1) || (minL >4) || (maxL<1) || (maxL>4) ||(i>=cmdLen)){
		 sprintf((char*)&atResponse.data,"+ERROR(2)\r\n");
		 LOG_DEBUG("conInterval parameter length error (%d,%d)\r\n",minL,maxL);
	 }else{
		 u16 conMin=0;
		 u16 conMax=0;
		 u8 min[5]={0x00};
		 u8 max[5]={0x00};
		 memcpy(min,&cmd[10],minL);
		 memcpy(max,&cmd[i+1],maxL);

		 switch(minL){
			 case 2:
				 conMin = (min[0]-0x30)*10 + min[1]-0x30;
				 break;
			 case 3:
				 conMin = (min[0]-0x30)*100 + (min[1]-0x30)*10 + (min[2]-0x30);
				 break;
			 case 4:
				 conMin = (min[0]-0x30)*1000 + (min[1]-0x30)*100 + (min[2]-0x30)*10 + (min[3]-0x30);
				 break;

			 default :
				 break;
		 }

		 switch(maxL){
			 case 2:
				 conMax = (max[0]-0x30)*10 + max[1]-0x30;
				 break;
			 case 3:
				 conMax = (max[0]-0x30)*100 + (max[1]-0x30)*10 + (max[2]-0x30);
				 break;
			 case 4:
				 conMax = (max[0]-0x30)*1000 + (max[1]-0x30)*100 + (max[2]-0x30)*10 + (max[3]-0x30);
				 break;

			 default :
				 break;
		 }

		 if((conMax<conMin)|| (conMin<6) ||(conMax>3200)){
			sprintf((char*)&atResponse.data,"+ERROR(3)\r\n");
			LOG_DEBUG("conItercal parameter range error(%d,%d)\r\n",conMin,conMax);
		 }else{
			paraToFlash.conIntervalMin = conMin;
			paraToFlash.conIntervalMax = conMax;
			appSystemParamaterSave();
			sprintf((char*)&atResponse.data,"+CONINT OK\r\n");
			LOG_DEBUG("set conn parameters ok\r\n");
		 }
	 }

	}else{
	  sprintf((char*)&atResponse.data,"+ERROR(1)\r\n");
	}
	atResponse.length = my_strlen((char*)&atResponse.data);
	appAtResponse(atResponse.data,atResponse.length,source);
}


_attribute_ram_code_ int appUartOrGattReceiveHandle(unsigned char *rxData, int rxLength,tsAtSource_t source){

	if(atRxBusy){
		return 1;
	}
	atRxBusy = 1;
    char *cs = NULL;
    unsigned char i=0;
  //spp.cmd_on_handle = 1;

  tlkapi_send_string_data(APP_LOG_EN,"[USER][LOG]UART RECEIVE",rxData,rxLength);

  if((rxLength == 4) && (rxData[0]=='A') && (rxData[1] == 'T')){
      i=0;
  }else{
      for(i=1;i<(AT_INLEGAL_CMD+1);i++){
          cs = my_strstr((const char*)rxData,(const char*)&atCmdList[i][0]);
          if(cs) break;
      }
  }
  LOG_DEBUG("at index = %d\r\n",i);
  //clear rsp buff

  memset(&atResponse,0,sizeof(atResponse));
  switch (i)
  {
  case AT:
    /* code */
	  atTest(source);
    break;

  case AT_VER:
    /* code */
	  atGetSoftwareVsersion(source);
    break;

  case AT_RSTN:
    /* code */
    atSoftwareReset(source);
    break;

  case AT_RSTORE:
    /* code */
    atSoftwareRestore(source);
    break;

  case AT_BRATE:
    /* code */
    atSetBrate(rxData,rxLength,source);
    break;

  case AT_AUTH:
    /* code */
    atAuth(rxData,rxLength,source);
    break;

  case AT_MAC:
    /* code */
    atBleMac(rxData,rxLength,source);
    break;

  case AT_POWER:
    /* code */
    atBleTxPower(rxData,rxLength,source);
    break;

  case AT_ADVNAME:
    /* code */
    atBleAdvNAME(rxData,rxLength,source);
    break;

  case AT_ADVDATA:
    /* code */
    atBleAdvData(rxData,rxLength,source);
    break;

   case AT_ADVINT:
    /* code */
    atBleAdvInterval(rxData,rxLength,source);
    break;   

  case AT_CONPARA:
    /* code */
    atBleConnPara(rxData,rxLength,source);
    break;

  default:
	  if(source == AT_UART){
		  bls_att_pushNotifyData(SPP_SERVER_TO_CLIENT_DP_H,rxData,rxLength);
	  }else{
		  bls_att_pushNotifyData(CMD_CLIENT_TO_SERVER_DP_H,rxData,rxLength);
	  }

    break;
  }

    atRxBusy = 0;
    return atRxBusy;
}


void appAtCmdRemainProc(void){

	if(((atRemainTime.cmd & AT_REMAIN_RSTN) == AT_REMAIN_RSTN) && clock_time_exceed(atRemainTime.tick[0],200*1000)){
		LOG_DEBUG("AT_REMAIN_RSTN action\r\n");
		atRemainTime.cmd &= (~AT_REMAIN_RSTN);
		reg_pwdn_ctrl|=FLD_PWDN_CTRL_REBOOT;  //soft reset

	}else if(((atRemainTime.cmd & AT_REMAIN_RSTORE) == AT_REMAIN_RSTORE) && clock_time_exceed(atRemainTime.tick[1],200*1000)){
		LOG_DEBUG("AT_REMAIN_RSTORE action\r\n");
		atRemainTime.cmd &= (~AT_REMAIN_RSTORE);
		flash_erase_sector(SYSTEM_PARA_ADDR_BASE);
		reg_pwdn_ctrl|=FLD_PWDN_CTRL_REBOOT;  //soft RESTORE

	}else if(((atRemainTime.cmd & AT_REMAIN_BRATE) == AT_REMAIN_BRATE) && clock_time_exceed(atRemainTime.tick[0],200*1000)){
		LOG_DEBUG("AT_REMAIN_BRATE action,new brate=%d\r\n",paraToFlash.brate);
		app_reinit_uartCB(paraToFlash.brate);
		atRemainTime.cmd &= (~AT_REMAIN_BRATE);//BUADRATE CHANGE
	}

}

//_attribute_ram_code_ int appGattReceiveHandle(unsigned char *rxData, int rxLength){
//    char *cs = NULL;
//    unsigned char i=0;
//  //spp.cmd_on_handle = 1;
//  tlkapi_send_string_data(APP_LOG_EN,"[USER][LOG]GATT RECEIVE",rxData,rxLength);
//   gattRxBusy = 1;
//  if((rxLength == 4) && (rxData[0]=='A') && (rxData[1] == 'T')){
//      i=0;
//  }else{
//      for(i=1;i<AT_CMD_NUM_MAX;i++){
//          cs = my_strstr((const char*)rxData,(const char*)&atCmdList[i][0]);
//          if(cs) break;
//      }
//  }
//
//  //clear rsp buff
//  LOG_DEBUG("gatt at index = %d\r\n",i);
//  memset(&AtResponse,0,sizeof(AtResponse));
//  switch (i)
//  {
//  case AT:
//    /* code */
//    break;
//
//  case AT_VER:
//    /* code */
//    break;
//
//  case AT_RSTN:
//    /* code */
//    break;
//
//  case AT_RSTORE:
//    /* code */
//    break;
//
//  case AT_BRATE:
//    /* code */
//    break;
//
//  case AT_MAC:
//    /* code */
//    break;
//
//  case AT_POWER:
//    /* code */
//    break;
//
//  case AT_ADVNAME:
//    /* code */
//    break;
//
//  case AT_ADVDATA:
//    /* code */
//    break;
//
//   case AT_ADVINT:
//    /* code */
//    break;
//
//  case AT_CONPARA:
//    /* code */
//    break;
//
//  default:
//    bls_att_pushNotifyData(CMD_CLIENT_TO_SERVER_DP_H,rxData,rxLength);
//    break;
//  }
//
//    gattRxBusy = 0;
//}
