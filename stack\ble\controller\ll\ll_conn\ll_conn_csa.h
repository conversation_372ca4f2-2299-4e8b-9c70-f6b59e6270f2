/********************************************************************************************************
 * @file    ll_conn_csa.h
 *
 * @brief   This is the header file for BLE SDK
 *
 * <AUTHOR> GROUP
 * @date    06,2022
 *
 * @par     Copyright (c) 2022, Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *
 *******************************************************************************************************/
#ifndef LL_CONN_CSA_H_
#define LL_CONN_CSA_H_




/**
 * @brief      this function is used to initialize channel selection algorithm #2 feature
 * @param	   none
 * @return     none
 */
void blc_ll_initChannelSelectionAlgorithm_2_feature(void);







#endif /* LL_CONN_CSA_H_ */
