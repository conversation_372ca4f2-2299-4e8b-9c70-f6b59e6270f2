# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\Telink_Project\FN_Project

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\Telink_Project\FN_Project\cmake_build

# Include any dependencies generated for this target.
include CMakeFiles/825x_ble_sample.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/825x_ble_sample.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/825x_ble_sample.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/825x_ble_sample.dir/flags.make

CMakeFiles/825x_ble_sample.dir/codegen:
.PHONY : CMakeFiles/825x_ble_sample.dir/codegen

CMakeFiles/825x_ble_sample.dir/div_mod.S.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/div_mod.S.obj: D:/Telink_Project/FN_Project/div_mod.S
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building ASM object CMakeFiles/825x_ble_sample.dir/div_mod.S.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -o CMakeFiles\825x_ble_sample.dir\div_mod.S.obj -c D:\Telink_Project\FN_Project\div_mod.S

CMakeFiles/825x_ble_sample.dir/div_mod.S.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing ASM source to CMakeFiles/825x_ble_sample.dir/div_mod.S.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -E D:\Telink_Project\FN_Project\div_mod.S > CMakeFiles\825x_ble_sample.dir\div_mod.S.i

CMakeFiles/825x_ble_sample.dir/div_mod.S.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling ASM source to assembly CMakeFiles/825x_ble_sample.dir/div_mod.S.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -S D:\Telink_Project\FN_Project\div_mod.S -o CMakeFiles\825x_ble_sample.dir\div_mod.S.s

CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.obj: D:/Telink_Project/FN_Project/application/app/usbaud.c
CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.obj -MF CMakeFiles\825x_ble_sample.dir\application\app\usbaud.c.obj.d -o CMakeFiles\825x_ble_sample.dir\application\app\usbaud.c.obj -c D:\Telink_Project\FN_Project\application\app\usbaud.c

CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\application\app\usbaud.c > CMakeFiles\825x_ble_sample.dir\application\app\usbaud.c.i

CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\application\app\usbaud.c -o CMakeFiles\825x_ble_sample.dir\application\app\usbaud.c.s

CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.obj: D:/Telink_Project/FN_Project/application/app/usbcdc.c
CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.obj -MF CMakeFiles\825x_ble_sample.dir\application\app\usbcdc.c.obj.d -o CMakeFiles\825x_ble_sample.dir\application\app\usbcdc.c.obj -c D:\Telink_Project\FN_Project\application\app\usbcdc.c

CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\application\app\usbcdc.c > CMakeFiles\825x_ble_sample.dir\application\app\usbcdc.c.i

CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\application\app\usbcdc.c -o CMakeFiles\825x_ble_sample.dir\application\app\usbcdc.c.s

CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.obj: D:/Telink_Project/FN_Project/application/app/usbkb.c
CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.obj -MF CMakeFiles\825x_ble_sample.dir\application\app\usbkb.c.obj.d -o CMakeFiles\825x_ble_sample.dir\application\app\usbkb.c.obj -c D:\Telink_Project\FN_Project\application\app\usbkb.c

CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\application\app\usbkb.c > CMakeFiles\825x_ble_sample.dir\application\app\usbkb.c.i

CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\application\app\usbkb.c -o CMakeFiles\825x_ble_sample.dir\application\app\usbkb.c.s

CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.obj: D:/Telink_Project/FN_Project/application/app/usbmouse.c
CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.obj -MF CMakeFiles\825x_ble_sample.dir\application\app\usbmouse.c.obj.d -o CMakeFiles\825x_ble_sample.dir\application\app\usbmouse.c.obj -c D:\Telink_Project\FN_Project\application\app\usbmouse.c

CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\application\app\usbmouse.c > CMakeFiles\825x_ble_sample.dir\application\app\usbmouse.c.i

CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\application\app\usbmouse.c -o CMakeFiles\825x_ble_sample.dir\application\app\usbmouse.c.s

CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.obj: D:/Telink_Project/FN_Project/application/audio/adpcm.c
CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.obj -MF CMakeFiles\825x_ble_sample.dir\application\audio\adpcm.c.obj.d -o CMakeFiles\825x_ble_sample.dir\application\audio\adpcm.c.obj -c D:\Telink_Project\FN_Project\application\audio\adpcm.c

CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\application\audio\adpcm.c > CMakeFiles\825x_ble_sample.dir\application\audio\adpcm.c.i

CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\application\audio\adpcm.c -o CMakeFiles\825x_ble_sample.dir\application\audio\adpcm.c.s

CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.obj: D:/Telink_Project/FN_Project/application/audio/gl_audio.c
CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.obj -MF CMakeFiles\825x_ble_sample.dir\application\audio\gl_audio.c.obj.d -o CMakeFiles\825x_ble_sample.dir\application\audio\gl_audio.c.obj -c D:\Telink_Project\FN_Project\application\audio\gl_audio.c

CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\application\audio\gl_audio.c > CMakeFiles\825x_ble_sample.dir\application\audio\gl_audio.c.i

CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\application\audio\gl_audio.c -o CMakeFiles\825x_ble_sample.dir\application\audio\gl_audio.c.s

CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.obj: D:/Telink_Project/FN_Project/application/audio/tl_audio.c
CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.obj -MF CMakeFiles\825x_ble_sample.dir\application\audio\tl_audio.c.obj.d -o CMakeFiles\825x_ble_sample.dir\application\audio\tl_audio.c.obj -c D:\Telink_Project\FN_Project\application\audio\tl_audio.c

CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\application\audio\tl_audio.c > CMakeFiles\825x_ble_sample.dir\application\audio\tl_audio.c.i

CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\application\audio\tl_audio.c -o CMakeFiles\825x_ble_sample.dir\application\audio\tl_audio.c.s

CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.obj: D:/Telink_Project/FN_Project/application/keyboard/keyboard.c
CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.obj -MF CMakeFiles\825x_ble_sample.dir\application\keyboard\keyboard.c.obj.d -o CMakeFiles\825x_ble_sample.dir\application\keyboard\keyboard.c.obj -c D:\Telink_Project\FN_Project\application\keyboard\keyboard.c

CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\application\keyboard\keyboard.c > CMakeFiles\825x_ble_sample.dir\application\keyboard\keyboard.c.i

CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\application\keyboard\keyboard.c -o CMakeFiles\825x_ble_sample.dir\application\keyboard\keyboard.c.s

CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.obj: D:/Telink_Project/FN_Project/application/print/putchar.c
CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.obj -MF CMakeFiles\825x_ble_sample.dir\application\print\putchar.c.obj.d -o CMakeFiles\825x_ble_sample.dir\application\print\putchar.c.obj -c D:\Telink_Project\FN_Project\application\print\putchar.c

CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\application\print\putchar.c > CMakeFiles\825x_ble_sample.dir\application\print\putchar.c.i

CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\application\print\putchar.c -o CMakeFiles\825x_ble_sample.dir\application\print\putchar.c.s

CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.obj: D:/Telink_Project/FN_Project/application/print/u_printf.c
CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.obj -MF CMakeFiles\825x_ble_sample.dir\application\print\u_printf.c.obj.d -o CMakeFiles\825x_ble_sample.dir\application\print\u_printf.c.obj -c D:\Telink_Project\FN_Project\application\print\u_printf.c

CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\application\print\u_printf.c > CMakeFiles\825x_ble_sample.dir\application\print\u_printf.c.i

CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\application\print\u_printf.c -o CMakeFiles\825x_ble_sample.dir\application\print\u_printf.c.s

CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.obj: D:/Telink_Project/FN_Project/application/usbstd/usb.c
CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.obj -MF CMakeFiles\825x_ble_sample.dir\application\usbstd\usb.c.obj.d -o CMakeFiles\825x_ble_sample.dir\application\usbstd\usb.c.obj -c D:\Telink_Project\FN_Project\application\usbstd\usb.c

CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\application\usbstd\usb.c > CMakeFiles\825x_ble_sample.dir\application\usbstd\usb.c.i

CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\application\usbstd\usb.c -o CMakeFiles\825x_ble_sample.dir\application\usbstd\usb.c.s

CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.obj: D:/Telink_Project/FN_Project/application/usbstd/usbdesc.c
CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.obj -MF CMakeFiles\825x_ble_sample.dir\application\usbstd\usbdesc.c.obj.d -o CMakeFiles\825x_ble_sample.dir\application\usbstd\usbdesc.c.obj -c D:\Telink_Project\FN_Project\application\usbstd\usbdesc.c

CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\application\usbstd\usbdesc.c > CMakeFiles\825x_ble_sample.dir\application\usbstd\usbdesc.c.i

CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\application\usbstd\usbdesc.c -o CMakeFiles\825x_ble_sample.dir\application\usbstd\usbdesc.c.s

CMakeFiles/825x_ble_sample.dir/boot/B85/cstartup_825x.S.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/boot/B85/cstartup_825x.S.obj: D:/Telink_Project/FN_Project/boot/B85/cstartup_825x.S
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building ASM object CMakeFiles/825x_ble_sample.dir/boot/B85/cstartup_825x.S.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -o CMakeFiles\825x_ble_sample.dir\boot\B85\cstartup_825x.S.obj -c D:\Telink_Project\FN_Project\boot\B85\cstartup_825x.S

CMakeFiles/825x_ble_sample.dir/boot/B85/cstartup_825x.S.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing ASM source to CMakeFiles/825x_ble_sample.dir/boot/B85/cstartup_825x.S.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -E D:\Telink_Project\FN_Project\boot\B85\cstartup_825x.S > CMakeFiles\825x_ble_sample.dir\boot\B85\cstartup_825x.S.i

CMakeFiles/825x_ble_sample.dir/boot/B85/cstartup_825x.S.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling ASM source to assembly CMakeFiles/825x_ble_sample.dir/boot/B85/cstartup_825x.S.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -S D:\Telink_Project\FN_Project\boot\B85\cstartup_825x.S -o CMakeFiles\825x_ble_sample.dir\boot\B85\cstartup_825x.S.s

CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.obj: D:/Telink_Project/FN_Project/common/sdk_version.c
CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.obj -MF CMakeFiles\825x_ble_sample.dir\common\sdk_version.c.obj.d -o CMakeFiles\825x_ble_sample.dir\common\sdk_version.c.obj -c D:\Telink_Project\FN_Project\common\sdk_version.c

CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\common\sdk_version.c > CMakeFiles\825x_ble_sample.dir\common\sdk_version.c.i

CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\common\sdk_version.c -o CMakeFiles\825x_ble_sample.dir\common\sdk_version.c.s

CMakeFiles/825x_ble_sample.dir/common/string.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/common/string.c.obj: D:/Telink_Project/FN_Project/common/string.c
CMakeFiles/825x_ble_sample.dir/common/string.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object CMakeFiles/825x_ble_sample.dir/common/string.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/common/string.c.obj -MF CMakeFiles\825x_ble_sample.dir\common\string.c.obj.d -o CMakeFiles\825x_ble_sample.dir\common\string.c.obj -c D:\Telink_Project\FN_Project\common\string.c

CMakeFiles/825x_ble_sample.dir/common/string.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/common/string.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\common\string.c > CMakeFiles\825x_ble_sample.dir\common\string.c.i

CMakeFiles/825x_ble_sample.dir/common/string.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/common/string.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\common\string.c -o CMakeFiles\825x_ble_sample.dir\common\string.c.s

CMakeFiles/825x_ble_sample.dir/common/utility.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/common/utility.c.obj: D:/Telink_Project/FN_Project/common/utility.c
CMakeFiles/825x_ble_sample.dir/common/utility.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object CMakeFiles/825x_ble_sample.dir/common/utility.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/common/utility.c.obj -MF CMakeFiles\825x_ble_sample.dir\common\utility.c.obj.d -o CMakeFiles\825x_ble_sample.dir\common\utility.c.obj -c D:\Telink_Project\FN_Project\common\utility.c

CMakeFiles/825x_ble_sample.dir/common/utility.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/common/utility.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\common\utility.c > CMakeFiles\825x_ble_sample.dir\common\utility.c.i

CMakeFiles/825x_ble_sample.dir/common/utility.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/common/utility.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\common\utility.c -o CMakeFiles\825x_ble_sample.dir\common\utility.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.obj: D:/Telink_Project/FN_Project/drivers/8258/adc.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\adc.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\adc.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\adc.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\adc.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\adc.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\adc.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\adc.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.obj: D:/Telink_Project/FN_Project/drivers/8258/aes.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\aes.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\aes.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\aes.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\aes.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\aes.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\aes.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\aes.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.obj: D:/Telink_Project/FN_Project/drivers/8258/analog.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\analog.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\analog.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\analog.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\analog.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\analog.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\analog.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\analog.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.obj: D:/Telink_Project/FN_Project/drivers/8258/audio.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\audio.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\audio.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\audio.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\audio.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\audio.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\audio.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\audio.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.obj: D:/Telink_Project/FN_Project/drivers/8258/bsp.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\bsp.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\bsp.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\bsp.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\bsp.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\bsp.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\bsp.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\bsp.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.obj: D:/Telink_Project/FN_Project/drivers/8258/clock.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\clock.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\clock.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\clock.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\clock.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\clock.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\clock.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\clock.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.obj: D:/Telink_Project/FN_Project/drivers/8258/driver_ext/ext_calibration.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\driver_ext\ext_calibration.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\driver_ext\ext_calibration.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\driver_ext\ext_calibration.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\driver_ext\ext_calibration.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\driver_ext\ext_calibration.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\driver_ext\ext_calibration.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\driver_ext\ext_calibration.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.obj: D:/Telink_Project/FN_Project/drivers/8258/driver_ext/ext_misc.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\driver_ext\ext_misc.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\driver_ext\ext_misc.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\driver_ext\ext_misc.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\driver_ext\ext_misc.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\driver_ext\ext_misc.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\driver_ext\ext_misc.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\driver_ext\ext_misc.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.obj: D:/Telink_Project/FN_Project/drivers/8258/driver_ext/rf_pa.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\driver_ext\rf_pa.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\driver_ext\rf_pa.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\driver_ext\rf_pa.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\driver_ext\rf_pa.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\driver_ext\rf_pa.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\driver_ext\rf_pa.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\driver_ext\rf_pa.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.obj: D:/Telink_Project/FN_Project/drivers/8258/driver_ext/software_uart.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\driver_ext\software_uart.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\driver_ext\software_uart.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\driver_ext\software_uart.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\driver_ext\software_uart.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\driver_ext\software_uart.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\driver_ext\software_uart.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\driver_ext\software_uart.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.obj: D:/Telink_Project/FN_Project/drivers/8258/emi.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\emi.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\emi.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\emi.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\emi.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\emi.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\emi.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\emi.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.obj: D:/Telink_Project/FN_Project/drivers/8258/flash.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\flash.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\flash.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\flash.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\flash.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\flash.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.obj: D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid011460c8.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid011460c8.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid011460c8.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid011460c8.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid011460c8.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid011460c8.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid011460c8.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid011460c8.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.obj: D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid1060c8.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid1060c8.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid1060c8.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid1060c8.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid1060c8.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid1060c8.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid1060c8.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid1060c8.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.obj: D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid13325e.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid13325e.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid13325e.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid13325e.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid13325e.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid13325e.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid13325e.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid13325e.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.obj: D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid134051.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid134051.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid134051.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid134051.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid134051.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid134051.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid134051.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid134051.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.obj: D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid136085.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid136085.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid136085.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid136085.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid136085.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid136085.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid136085.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid136085.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.obj: D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid1360c8.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid1360c8.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid1360c8.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid1360c8.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid1360c8.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid1360c8.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid1360c8.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid1360c8.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.obj: D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid1360eb.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid1360eb.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid1360eb.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid1360eb.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid1360eb.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid1360eb.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid1360eb.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid1360eb.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.obj: D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid14325e.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid14325e.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid14325e.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid14325e.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid14325e.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid14325e.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid14325e.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid14325e.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.obj: D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid1460c8.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid1460c8.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid1460c8.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid1460c8.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid1460c8.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid1460c8.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\flash\flash_mid1460c8.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\flash\flash_mid1460c8.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.obj: D:/Telink_Project/FN_Project/drivers/8258/gpio.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\gpio.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\gpio.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\gpio.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\gpio.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\gpio.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\gpio.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\gpio.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.obj: D:/Telink_Project/FN_Project/drivers/8258/i2c.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\i2c.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\i2c.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\i2c.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\i2c.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\i2c.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\i2c.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\i2c.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.obj: D:/Telink_Project/FN_Project/drivers/8258/lpc.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\lpc.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\lpc.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\lpc.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\lpc.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\lpc.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\lpc.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\lpc.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.obj: D:/Telink_Project/FN_Project/drivers/8258/qdec.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\qdec.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\qdec.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\qdec.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\qdec.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\qdec.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\qdec.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\qdec.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.obj: D:/Telink_Project/FN_Project/drivers/8258/s7816.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\s7816.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\s7816.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\s7816.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\s7816.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\s7816.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\s7816.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\s7816.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.obj: D:/Telink_Project/FN_Project/drivers/8258/spi.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_44) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\spi.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\spi.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\spi.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\spi.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\spi.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\spi.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\spi.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.obj: D:/Telink_Project/FN_Project/drivers/8258/timer.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_45) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\timer.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\timer.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\timer.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\timer.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\timer.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\timer.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\timer.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.obj: D:/Telink_Project/FN_Project/drivers/8258/uart.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_46) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\uart.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\uart.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\uart.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\uart.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\uart.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\uart.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\uart.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.obj: D:/Telink_Project/FN_Project/drivers/8258/usbhw.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_47) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\usbhw.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\usbhw.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\usbhw.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\usbhw.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\usbhw.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\usbhw.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\usbhw.c.s

CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.obj: D:/Telink_Project/FN_Project/drivers/8258/watchdog.c
CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_48) "Building C object CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.obj -MF CMakeFiles\825x_ble_sample.dir\drivers\8258\watchdog.c.obj.d -o CMakeFiles\825x_ble_sample.dir\drivers\8258\watchdog.c.obj -c D:\Telink_Project\FN_Project\drivers\8258\watchdog.c

CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\drivers\8258\watchdog.c > CMakeFiles\825x_ble_sample.dir\drivers\8258\watchdog.c.i

CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\drivers\8258\watchdog.c -o CMakeFiles\825x_ble_sample.dir\drivers\8258\watchdog.c.s

CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.obj: D:/Telink_Project/FN_Project/vendor/b85m_ble_sample/app.c
CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_49) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\b85m_ble_sample\app.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\b85m_ble_sample\app.c.obj -c D:\Telink_Project\FN_Project\vendor\b85m_ble_sample\app.c

CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\b85m_ble_sample\app.c > CMakeFiles\825x_ble_sample.dir\vendor\b85m_ble_sample\app.c.i

CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\b85m_ble_sample\app.c -o CMakeFiles\825x_ble_sample.dir\vendor\b85m_ble_sample\app.c.s

CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.obj: D:/Telink_Project/FN_Project/vendor/b85m_ble_sample/app_att.c
CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_50) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\b85m_ble_sample\app_att.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\b85m_ble_sample\app_att.c.obj -c D:\Telink_Project\FN_Project\vendor\b85m_ble_sample\app_att.c

CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\b85m_ble_sample\app_att.c > CMakeFiles\825x_ble_sample.dir\vendor\b85m_ble_sample\app_att.c.i

CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\b85m_ble_sample\app_att.c -o CMakeFiles\825x_ble_sample.dir\vendor\b85m_ble_sample\app_att.c.s

CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.obj: D:/Telink_Project/FN_Project/vendor/b85m_ble_sample/app_ui.c
CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_51) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\b85m_ble_sample\app_ui.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\b85m_ble_sample\app_ui.c.obj -c D:\Telink_Project\FN_Project\vendor\b85m_ble_sample\app_ui.c

CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\b85m_ble_sample\app_ui.c > CMakeFiles\825x_ble_sample.dir\vendor\b85m_ble_sample\app_ui.c.i

CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\b85m_ble_sample\app_ui.c -o CMakeFiles\825x_ble_sample.dir\vendor\b85m_ble_sample\app_ui.c.s

CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.obj: D:/Telink_Project/FN_Project/vendor/b85m_ble_sample/main.c
CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_52) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\b85m_ble_sample\main.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\b85m_ble_sample\main.c.obj -c D:\Telink_Project\FN_Project\vendor\b85m_ble_sample\main.c

CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\b85m_ble_sample\main.c > CMakeFiles\825x_ble_sample.dir\vendor\b85m_ble_sample\main.c.i

CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\b85m_ble_sample\main.c -o CMakeFiles\825x_ble_sample.dir\vendor\b85m_ble_sample\main.c.s

CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.obj: D:/Telink_Project/FN_Project/vendor/common/app_buffer.c
CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_53) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\common\app_buffer.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\common\app_buffer.c.obj -c D:\Telink_Project\FN_Project\vendor\common\app_buffer.c

CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\common\app_buffer.c > CMakeFiles\825x_ble_sample.dir\vendor\common\app_buffer.c.i

CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\common\app_buffer.c -o CMakeFiles\825x_ble_sample.dir\vendor\common\app_buffer.c.s

CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.obj: D:/Telink_Project/FN_Project/vendor/common/app_common.c
CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_54) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\common\app_common.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\common\app_common.c.obj -c D:\Telink_Project\FN_Project\vendor\common\app_common.c

CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\common\app_common.c > CMakeFiles\825x_ble_sample.dir\vendor\common\app_common.c.i

CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\common\app_common.c -o CMakeFiles\825x_ble_sample.dir\vendor\common\app_common.c.s

CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.obj: D:/Telink_Project/FN_Project/vendor/common/battery_check.c
CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_55) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\common\battery_check.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\common\battery_check.c.obj -c D:\Telink_Project\FN_Project\vendor\common\battery_check.c

CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\common\battery_check.c > CMakeFiles\825x_ble_sample.dir\vendor\common\battery_check.c.i

CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\common\battery_check.c -o CMakeFiles\825x_ble_sample.dir\vendor\common\battery_check.c.s

CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.obj: D:/Telink_Project/FN_Project/vendor/common/ble_flash.c
CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_56) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\common\ble_flash.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\common\ble_flash.c.obj -c D:\Telink_Project\FN_Project\vendor\common\ble_flash.c

CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\common\ble_flash.c > CMakeFiles\825x_ble_sample.dir\vendor\common\ble_flash.c.i

CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\common\ble_flash.c -o CMakeFiles\825x_ble_sample.dir\vendor\common\ble_flash.c.s

CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.obj: D:/Telink_Project/FN_Project/vendor/common/blt_fw_sign.c
CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_57) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\common\blt_fw_sign.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\common\blt_fw_sign.c.obj -c D:\Telink_Project\FN_Project\vendor\common\blt_fw_sign.c

CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\common\blt_fw_sign.c > CMakeFiles\825x_ble_sample.dir\vendor\common\blt_fw_sign.c.i

CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\common\blt_fw_sign.c -o CMakeFiles\825x_ble_sample.dir\vendor\common\blt_fw_sign.c.s

CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.obj: D:/Telink_Project/FN_Project/vendor/common/blt_led.c
CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_58) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\common\blt_led.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\common\blt_led.c.obj -c D:\Telink_Project\FN_Project\vendor\common\blt_led.c

CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\common\blt_led.c > CMakeFiles\825x_ble_sample.dir\vendor\common\blt_led.c.i

CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\common\blt_led.c -o CMakeFiles\825x_ble_sample.dir\vendor\common\blt_led.c.s

CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.obj: D:/Telink_Project/FN_Project/vendor/common/blt_soft_timer.c
CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_59) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\common\blt_soft_timer.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\common\blt_soft_timer.c.obj -c D:\Telink_Project\FN_Project\vendor\common\blt_soft_timer.c

CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\common\blt_soft_timer.c > CMakeFiles\825x_ble_sample.dir\vendor\common\blt_soft_timer.c.i

CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\common\blt_soft_timer.c -o CMakeFiles\825x_ble_sample.dir\vendor\common\blt_soft_timer.c.s

CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.obj: D:/Telink_Project/FN_Project/vendor/common/custom_pair.c
CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_60) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\common\custom_pair.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\common\custom_pair.c.obj -c D:\Telink_Project\FN_Project\vendor\common\custom_pair.c

CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\common\custom_pair.c > CMakeFiles\825x_ble_sample.dir\vendor\common\custom_pair.c.i

CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\common\custom_pair.c -o CMakeFiles\825x_ble_sample.dir\vendor\common\custom_pair.c.s

CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.obj: D:/Telink_Project/FN_Project/vendor/common/flash_fw_check.c
CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_61) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\common\flash_fw_check.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\common\flash_fw_check.c.obj -c D:\Telink_Project\FN_Project\vendor\common\flash_fw_check.c

CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\common\flash_fw_check.c > CMakeFiles\825x_ble_sample.dir\vendor\common\flash_fw_check.c.i

CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\common\flash_fw_check.c -o CMakeFiles\825x_ble_sample.dir\vendor\common\flash_fw_check.c.s

CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.obj: D:/Telink_Project/FN_Project/vendor/common/flash_prot.c
CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_62) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\common\flash_prot.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\common\flash_prot.c.obj -c D:\Telink_Project\FN_Project\vendor\common\flash_prot.c

CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\common\flash_prot.c > CMakeFiles\825x_ble_sample.dir\vendor\common\flash_prot.c.i

CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\common\flash_prot.c -o CMakeFiles\825x_ble_sample.dir\vendor\common\flash_prot.c.s

CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.obj: D:/Telink_Project/FN_Project/vendor/common/simple_sdp.c
CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_63) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\common\simple_sdp.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\common\simple_sdp.c.obj -c D:\Telink_Project\FN_Project\vendor\common\simple_sdp.c

CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\common\simple_sdp.c > CMakeFiles\825x_ble_sample.dir\vendor\common\simple_sdp.c.i

CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\common\simple_sdp.c -o CMakeFiles\825x_ble_sample.dir\vendor\common\simple_sdp.c.s

CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.obj: D:/Telink_Project/FN_Project/vendor/common/tlkapi_debug.c
CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_64) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\common\tlkapi_debug.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\common\tlkapi_debug.c.obj -c D:\Telink_Project\FN_Project\vendor\common\tlkapi_debug.c

CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\common\tlkapi_debug.c > CMakeFiles\825x_ble_sample.dir\vendor\common\tlkapi_debug.c.i

CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\common\tlkapi_debug.c -o CMakeFiles\825x_ble_sample.dir\vendor\common\tlkapi_debug.c.s

CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.obj: D:/Telink_Project/FN_Project/vendor/common/user_config.c
CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_65) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\common\user_config.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\common\user_config.c.obj -c D:\Telink_Project\FN_Project\vendor\common\user_config.c

CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\common\user_config.c > CMakeFiles\825x_ble_sample.dir\vendor\common\user_config.c.i

CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\common\user_config.c -o CMakeFiles\825x_ble_sample.dir\vendor\common\user_config.c.s

CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/fn/fn_agreement.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/fn/fn_agreement.c.obj: D:/Telink_Project/FN_Project/vendor/user_app/agreement/fn/fn_agreement.c
CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/fn/fn_agreement.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_66) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/fn/fn_agreement.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/fn/fn_agreement.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\user_app\agreement\fn\fn_agreement.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\agreement\fn\fn_agreement.c.obj -c D:\Telink_Project\FN_Project\vendor\user_app\agreement\fn\fn_agreement.c

CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/fn/fn_agreement.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/fn/fn_agreement.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\user_app\agreement\fn\fn_agreement.c > CMakeFiles\825x_ble_sample.dir\vendor\user_app\agreement\fn\fn_agreement.c.i

CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/fn/fn_agreement.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/fn/fn_agreement.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\user_app\agreement\fn\fn_agreement.c -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\agreement\fn\fn_agreement.c.s

CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.obj: D:/Telink_Project/FN_Project/vendor/user_app/at_cmd/app_at_cmd.c
CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_67) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\user_app\at_cmd\app_at_cmd.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\at_cmd\app_at_cmd.c.obj -c D:\Telink_Project\FN_Project\vendor\user_app\at_cmd\app_at_cmd.c

CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\user_app\at_cmd\app_at_cmd.c > CMakeFiles\825x_ble_sample.dir\vendor\user_app\at_cmd\app_at_cmd.c.i

CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\user_app\at_cmd\app_at_cmd.c -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\at_cmd\app_at_cmd.c.s

CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.obj: D:/Telink_Project/FN_Project/vendor/user_app/att_ble/app_ble.c
CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_68) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\user_app\att_ble\app_ble.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\att_ble\app_ble.c.obj -c D:\Telink_Project\FN_Project\vendor\user_app\att_ble\app_ble.c

CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\user_app\att_ble\app_ble.c > CMakeFiles\825x_ble_sample.dir\vendor\user_app\att_ble\app_ble.c.i

CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\user_app\att_ble\app_ble.c -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\att_ble\app_ble.c.s

CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.obj: D:/Telink_Project/FN_Project/vendor/user_app/bms/bms_data.c
CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_69) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\user_app\bms\bms_data.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\bms\bms_data.c.obj -c D:\Telink_Project\FN_Project\vendor\user_app\bms\bms_data.c

CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\user_app\bms\bms_data.c > CMakeFiles\825x_ble_sample.dir\vendor\user_app\bms\bms_data.c.i

CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\user_app\bms\bms_data.c -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\bms\bms_data.c.s

CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.obj: D:/Telink_Project/FN_Project/vendor/user_app/bms/zy/sh367601xb.c
CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_70) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\user_app\bms\zy\sh367601xb.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\bms\zy\sh367601xb.c.obj -c D:\Telink_Project\FN_Project\vendor\user_app\bms\zy\sh367601xb.c

CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\user_app\bms\zy\sh367601xb.c > CMakeFiles\825x_ble_sample.dir\vendor\user_app\bms\zy\sh367601xb.c.i

CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\user_app\bms\zy\sh367601xb.c -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\bms\zy\sh367601xb.c.s

CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_conversion.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_conversion.c.obj: D:/Telink_Project/FN_Project/vendor/user_app/bms/zy/sh367601xb_conversion.c
CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_conversion.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_71) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_conversion.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_conversion.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\user_app\bms\zy\sh367601xb_conversion.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\bms\zy\sh367601xb_conversion.c.obj -c D:\Telink_Project\FN_Project\vendor\user_app\bms\zy\sh367601xb_conversion.c

CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_conversion.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_conversion.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\user_app\bms\zy\sh367601xb_conversion.c > CMakeFiles\825x_ble_sample.dir\vendor\user_app\bms\zy\sh367601xb_conversion.c.i

CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_conversion.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_conversion.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\user_app\bms\zy\sh367601xb_conversion.c -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\bms\zy\sh367601xb_conversion.c.s

CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.obj: D:/Telink_Project/FN_Project/vendor/user_app/flash/user_app_flash.c
CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_72) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\user_app\flash\user_app_flash.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\flash\user_app_flash.c.obj -c D:\Telink_Project\FN_Project\vendor\user_app\flash\user_app_flash.c

CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\user_app\flash\user_app_flash.c > CMakeFiles\825x_ble_sample.dir\vendor\user_app\flash\user_app_flash.c.i

CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\user_app\flash\user_app_flash.c -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\flash\user_app_flash.c.s

CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.obj: D:/Telink_Project/FN_Project/vendor/user_app/list/queue/queue.c
CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_73) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\user_app\list\queue\queue.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\list\queue\queue.c.obj -c D:\Telink_Project\FN_Project\vendor\user_app\list\queue\queue.c

CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\user_app\list\queue\queue.c > CMakeFiles\825x_ble_sample.dir\vendor\user_app\list\queue\queue.c.i

CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\user_app\list\queue\queue.c -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\list\queue\queue.c.s

CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.obj: D:/Telink_Project/FN_Project/vendor/user_app/system_main/app_mian.c
CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_74) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\user_app\system_main\app_mian.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\system_main\app_mian.c.obj -c D:\Telink_Project\FN_Project\vendor\user_app\system_main\app_mian.c

CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\user_app\system_main\app_mian.c > CMakeFiles\825x_ble_sample.dir\vendor\user_app\system_main\app_mian.c.i

CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\user_app\system_main\app_mian.c -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\system_main\app_mian.c.s

CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.obj: D:/Telink_Project/FN_Project/vendor/user_app/uart/app_usart.c
CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_75) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\user_app\uart\app_usart.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\uart\app_usart.c.obj -c D:\Telink_Project\FN_Project\vendor\user_app\uart\app_usart.c

CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\user_app\uart\app_usart.c > CMakeFiles\825x_ble_sample.dir\vendor\user_app\uart\app_usart.c.i

CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\user_app\uart\app_usart.c -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\uart\app_usart.c.s

CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.obj: D:/Telink_Project/FN_Project/vendor/user_app/user_app_main.c
CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_76) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\user_app\user_app_main.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\user_app_main.c.obj -c D:\Telink_Project\FN_Project\vendor\user_app\user_app_main.c

CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\user_app\user_app_main.c > CMakeFiles\825x_ble_sample.dir\vendor\user_app\user_app_main.c.i

CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\user_app\user_app_main.c -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\user_app_main.c.s

CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_rs2058.c.obj: CMakeFiles/825x_ble_sample.dir/flags.make
CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_rs2058.c.obj: D:/Telink_Project/FN_Project/vendor/user_app/user_app_rs2058.c
CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_rs2058.c.obj: CMakeFiles/825x_ble_sample.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_77) "Building C object CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_rs2058.c.obj"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_rs2058.c.obj -MF CMakeFiles\825x_ble_sample.dir\vendor\user_app\user_app_rs2058.c.obj.d -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\user_app_rs2058.c.obj -c D:\Telink_Project\FN_Project\vendor\user_app\user_app_rs2058.c

CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_rs2058.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_rs2058.c.i"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\Telink_Project\FN_Project\vendor\user_app\user_app_rs2058.c > CMakeFiles\825x_ble_sample.dir\vendor\user_app\user_app_rs2058.c.i

CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_rs2058.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_rs2058.c.s"
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\Telink_Project\FN_Project\vendor\user_app\user_app_rs2058.c -o CMakeFiles\825x_ble_sample.dir\vendor\user_app\user_app_rs2058.c.s

# Object files for target 825x_ble_sample
825x_ble_sample_OBJECTS = \
"CMakeFiles/825x_ble_sample.dir/div_mod.S.obj" \
"CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.obj" \
"CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.obj" \
"CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.obj" \
"CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.obj" \
"CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.obj" \
"CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.obj" \
"CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.obj" \
"CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.obj" \
"CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.obj" \
"CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.obj" \
"CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.obj" \
"CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.obj" \
"CMakeFiles/825x_ble_sample.dir/boot/B85/cstartup_825x.S.obj" \
"CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.obj" \
"CMakeFiles/825x_ble_sample.dir/common/string.c.obj" \
"CMakeFiles/825x_ble_sample.dir/common/utility.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.obj" \
"CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/fn/fn_agreement.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_conversion.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.obj" \
"CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_rs2058.c.obj"

# External object files for target 825x_ble_sample
825x_ble_sample_EXTERNAL_OBJECTS =

825x_ble_sample: CMakeFiles/825x_ble_sample.dir/div_mod.S.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/boot/B85/cstartup_825x.S.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/common/string.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/common/utility.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/fn/fn_agreement.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_conversion.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_rs2058.c.obj
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/build.make
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/linkLibs.rsp
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/objects1.rsp
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=D:\Telink_Project\FN_Project\cmake_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_78) "Linking C executable 825x_ble_sample"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\825x_ble_sample.dir\link.txt --verbose=$(VERBOSE)
	sh D:/Telink_Project/FN_Project/././tl_check_fw.sh 825x_ble_sample FN_Project
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-objcopy -O binary D:/Telink_Project/FN_Project/cmake_build/825x_ble_sample D:/Telink_Project/FN_Project/cmake_build/825x_ble_sample.bin
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-objdump -x -l -S -D D:/Telink_Project/FN_Project/cmake_build/825x_ble_sample > D:/Telink_Project/FN_Project/cmake_build/825x_ble_sample.lst
	C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32\bin\tc32-elf-size -t D:/Telink_Project/FN_Project/cmake_build/825x_ble_sample

# Rule to build all files generated by this target.
CMakeFiles/825x_ble_sample.dir/build: 825x_ble_sample
.PHONY : CMakeFiles/825x_ble_sample.dir/build

CMakeFiles/825x_ble_sample.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\825x_ble_sample.dir\cmake_clean.cmake
.PHONY : CMakeFiles/825x_ble_sample.dir/clean

CMakeFiles/825x_ble_sample.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\Telink_Project\FN_Project D:\Telink_Project\FN_Project D:\Telink_Project\FN_Project\cmake_build D:\Telink_Project\FN_Project\cmake_build D:\Telink_Project\FN_Project\cmake_build\CMakeFiles\825x_ble_sample.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/825x_ble_sample.dir/depend

