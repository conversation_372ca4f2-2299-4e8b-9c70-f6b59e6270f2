# BMS系统架构文档

## 📋 目录
- [系统概述](#系统概述)
- [文件结构](#文件结构)
- [核心架构](#核心架构)
- [数据管理层](#数据管理层)
- [硬件抽象层](#硬件抽象层)
- [数据转换层](#数据转换层)
- [设计模式](#设计模式)
- [数据流向](#数据流向)

## 🎯 系统概述

本BMS（电池管理系统）采用**分层架构**和**面向对象设计**，实现了高度模块化和可扩展的电池管理功能。系统支持多种BMS芯片，当前主要支持*********芯片。

### 核心特性
- ✅ **模块化设计**：各功能模块独立，便于维护和扩展
- ✅ **面向对象架构**：使用C语言实现面向对象设计模式
- ✅ **数据驱动**：基于统一的数据管理系统
- ✅ **硬件抽象**：支持多种BMS芯片的统一接口
- ✅ **专业算法**：集成温度补偿、数字滤波、SOC计算等专业功能

## 📁 文件结构

```
vendor/user_app/bms/
├── bms_data.h              # BMS数据管理系统核心头文件
├── bms_data.c              # BMS数据管理系统实现
└── zy/                     # 中颖电子(ZY)芯片驱动目录
    ├── sh367601xb.h        # *********芯片驱动头文件
    ├── sh367601xb.c        # *********芯片驱动实现
    ├── sh367601xb_conversion.h  # 数据转换函数库头文件
    └── sh367601xb_conversion.c  # 数据转换函数库实现
```

## 🏗️ 核心架构

### 三层架构设计

```
┌─────────────────────────────────────────┐
│           应用层 (Application)           │
│        业务逻辑 & 用户接口                 │
└─────────────────────────────────────────┘
                    ↕
┌─────────────────────────────────────────┐
│         数据管理层 (Data Management)     │
│     BMS_DataManager & 各种Manager      │
└─────────────────────────────────────────┘
                    ↕
┌─────────────────────────────────────────┐
│       硬件抽象层 (Hardware Abstraction) │
│        ********* Driver & Conversion    │
└─────────────────────────────────────────┘
                    ↕
┌─────────────────────────────────────────┐
│           硬件层 (Hardware)             │
│         BMS芯片 & 传感器               │
└─────────────────────────────────────────┘
```

## 📊 数据管理层

### BMS_DataManager（核心数据管理器）

**文件**: `bms_data.h` / `bms_data.c`

**职责**: 统一管理所有BMS数据，提供面向对象的数据访问接口

#### 核心组件

```c
typedef struct BMS_DataManager {
    /* 数据管理器实例 */
    AlarmManager alarm_mgr;                    // 告警管理器
    StatusManager status_mgr;                  // 状态管理器  
    VoltageManager voltage_mgr;                // 电压管理器
    CurrentManager current_mgr;                // 电流管理器
    ChargeDischargeManager charge_discharge_mgr; // 充放电管理器
    TemperatureManager temp_mgr;               // 温度管理器
    BatteryStateManager battery_state_mgr;     // 电池状态管理器
    ProtectionParameterManager protection_param_mgr; // 保护参数管理器
    CustomParameterManager custom_param_mgr;   // 自定义参数管理器
    
    /* 方法指针 */
    struct {
        void (*init)(struct BMS_DataManager* self);
    } methods;
} BMS_DataManager;
```

### 数据结构定义

#### 1. 告警数据 (AlarmData)
```c
typedef struct {
    bool charge_over_temp;      /* 充电高温保护 */
    bool charge_under_temp;     /* 充电低温保护 */
    bool discharge_over_temp;   /* 放电高温保护 */
    bool discharge_under_temp;  /* 放电低温保护 */
    bool charge_overcurrent;    /* 充电过流 */
    bool discharge_overcurrent1; /* 放电过流1 */
    bool discharge_overcurrent2; /* 放电过流2 */
    bool undervoltage;          /* 欠压保护 */
    bool overvoltage;           /* 过压保护 */
} AlarmData;
```

#### 2. 电压数据 (VoltageData)
```c
typedef struct {
    unsigned short total_voltage;     /* 总电压，单位：mV */
    unsigned short cell_voltages[16]; /* 单体电压，单位：mV */
    unsigned short max_voltage;       /* 最大电压，单位：mV */
    unsigned short min_voltage;       /* 最小电压，单位：mV */
    unsigned short voltage_diff;      /* 电池压差，单位：mV */
    unsigned char battery_count;      /* 电池串数 */
} VoltageData;
```

#### 3. 电流数据 (CurrentData)
```c
typedef struct {
    int total_current;          /* 总电流，单位：mA */
    int max_charge_current;     /* 最大充电电流，单位：mA */
    int max_discharge_current;  /* 最大放电电流，单位：mA */
    int battery_power;          /* 电池功率，单位：mW */
    CurrentState current_state; /* 电流状态：空闲/充电/放电 */
} CurrentData;
```

#### 4. 充放电管理数据 (ChargeDischargeData)
```c
typedef struct {
    unsigned long cycle_count;           /* 循环次数 */
    unsigned long remaining_capacity;    /* 剩余容量，单位：mAh */
    unsigned long remaining_time;        /* 剩余可用时间，单位：分钟 */
    unsigned long accumulated_charge;    /* 累计充电容量，单位：mAh */
    unsigned long accumulated_discharge; /* 累计放电容量，单位：mAh */
    unsigned long power;                /* 当前功率，单位：mW */
} ChargeDischargeData;
```

#### 5. 温度数据 (TemperatureData)
```c
typedef struct {
    unsigned char external_temp_count;   /* 外部温度数量 */
    unsigned char chip_temp_count;       /* 芯片温度数量 */
    unsigned char mos_temp_count;        /* MOS管温度数量 */
    signed char external_temp[10];      /* 外部温度，单位：℃ */
    signed char max_external_temp;      /* 最大外部温度，单位：℃ */
    signed char min_external_temp;      /* 最小外部温度，单位：℃ */
    signed char chip_temp;              /* 芯片温度，单位：℃ */
    signed char mos_temp;               /* MOS管温度，单位：℃ */
} TemperatureData;
```

#### 6. 电池状态数据 (BatteryStateData)
```c
typedef struct {
    unsigned short soc;              /* SOC电量百分比，0.01%精度 */
    unsigned short soh;              /* SOH健康状态，0.01%精度 */
    unsigned int sampling_time_last;     /* 采样时间（上一次），单位：ms */
    unsigned int sampling_time_curr;     /* 采样时间（新），单位：ms */
} BatteryStateData;
```

### 管理器初始化机制

使用宏定义实现统一的管理器初始化：

```c
#define DEFINE_MANAGER_INIT(ManagerType, DataType) \
static void manager_init_##ManagerType(ManagerType* self) { \
    if (self == NULL) return; \
    memset(&self->data, 0, sizeof(DataType)); \
    printf(#ManagerType ": Initialized\n"); \
}

#define INIT_AND_ENABLE_MANAGER(self, mgr_name, mgr_type, display_name) \
    if (manager_init_func_##mgr_type(&self->mgr_name) == 0) { \
        self->mgr_name##_enabled = true; \
        self->mgr_name.methods.init(&self->mgr_name); \
        printf(display_name " manager init success\n"); \
    }
```

## 🔧 硬件抽象层

### *********驱动 (sh367601xb.h/c)

**职责**: 提供*********芯片的完整驱动功能

#### 核心特性
- ✅ **寄存器抽象**：ROM/RAM寄存器的结构化访问
- ✅ **通信驱动**：I2C通信协议实现
- ✅ **数据同步**：硬件数据与BMS数据管理器的同步
- ✅ **专业处理**：集成专业的数据处理算法

#### 设备结构
```c
typedef struct *********_Device {
    /* 硬件寄存器 */
    *********_ROM rom;          // ROM寄存器
    *********_RAM ram;          // RAM寄存器
    
    /* BMS数据管理系统集成 */
    BMS_DataManager bms_system;
    
    /* 同步方法指针 */
    struct {
        void (*update_realtime_data)(*********_Device* self);
        void (*update_protection_config)(*********_Device* self);
        void (*process_current_data)(*********_Device* self, unsigned short raw_current);
        void (*process_voltage_data)(*********_Device* self);
        void (*process_temperature_data)(*********_Device* self);
        void (*process_battery_state_data)(*********_Device* self);
        void (*process_charge_discharge_data)(*********_Device* self);
        void (*process_alarm_data)(*********_Device* self);
        void (*process_status_data)(*********_Device* self);
    } bms_sync;
    
    /* 通信接口 */
    struct {
        int (*read_rom)(*********_Device* self);
        int (*write_rom)(*********_Device* self);
        int (*read_ram)(*********_Device* self);
        int (*read_current)(*********_Device* self);
    } comm;
} *********_Device;
```

#### 专业数据处理函数

**电流数据处理**:
```c
static void sh367601b_process_current_data(*********_Device* device, unsigned short raw_current)
```
- ADC转换：`current_calculate_from_adc()`
- 温度补偿：`current_apply_temp_compensation()`
- 数字滤波：`hybridFilter()`
- 状态判断：充电/放电/空闲状态
- 最大值记录：最大充电电流和最大放电电流

**电压数据处理**:
```c
static void sh367601b_process_voltage_data(*********_Device* device)
```
- 智能检测有效电池串数
- 计算总电压、最大最小值、压差
- 异常电压过滤

**温度数据处理**:
```c
static void sh367601b_process_temperature_data(*********_Device* device)
```
- NTC温度转换：`ntc_calculate_external_temp()`
- 温度统计：最大最小外部温度
- 多点温度监控

**电池状态处理**:
```c
static void sh367601b_process_battery_state_data(*********_Device* device)
```
- SOC计算：`update_soc_simple()`
- 采样时间管理

**充放电数据处理**:
```c
static void sh367601b_process_charge_discharge_data(*********_Device* device)
```
- 时间估算：剩余放电时间/充电到满时间
- 容量统计：累计充电/放电容量
- 循环计数：基于累计放电量计算
- 功率计算：实时功率更新

## 🔄 数据转换层

### 转换函数库 (sh367601xb_conversion.h/c)

**职责**: 提供专业的数据转换和处理算法

#### 核心功能模块

**1. NTC温度转换**
```c
extern signed char ntc_calculate_external_temp(unsigned short adc_value,
                                              const NTC_TypeDef* ntc_table,
                                              unsigned short table_size);
```
- 支持自定义NTC查找表
- 线性插值算法
- 温度偏移处理

**2. 电流ADC转换**
```c
extern int current_calculate_from_adc(unsigned short adc_value,
                                     float gain,
                                     float shunt_resistance_mohm,
                                     float adc_reference_mv);
```
- 高精度ADC转换
- 可配置增益和分流电阻
- 支持正负电流

**3. 电流温度补偿**
```c
extern float current_apply_temp_compensation(float current_ma,
                                           float temperature_c,
                                           float reference_temp_c,
                                           float temp_coefficient,
                                           float min_temp_c,
                                           float max_temp_c);
```
- 温度系数补偿
- 温度范围限制
- 线性补偿算法

**4. 数字滤波器**
```c
extern float hybridFilter(float new_sample);
```
- 混合SMA+EMA滤波
- 自适应滤波参数
- 噪声抑制

**5. 电压转换**
```c
extern unsigned short Reg_From_Voltage(unsigned short reg_value);
```
- 寄存器值到电压转换
- 精度校准

**6. SOC计算**
```c
extern float update_soc_simple(float soc, float capacity_mAh,
                              float current_mA, float time_ms,
                              unsigned char direction);
```
- 基于电流积分的SOC计算
- 充放电方向处理
- SOC边界保护

## 🎨 设计模式

### 1. 面向对象设计模式

**特点**:
- 使用结构体模拟类
- 方法指针实现多态
- 数据封装和访问控制

**示例**:
```c
typedef struct AlarmManager {
    AlarmData data;                    // 数据成员
    struct {                          // 方法指针
        void (*init)(struct AlarmManager* self);
    } methods;
} AlarmManager;
```

### 2. 工厂模式

**宏定义工厂**:
```c
#define DEFINE_MANAGER_INIT(ManagerType, DataType) \
static void manager_init_##ManagerType(ManagerType* self) { \
    /* 统一初始化逻辑 */ \
}
```

### 3. 策略模式

**数据处理策略**:
```c
struct {
    void (*process_current_data)(*********_Device* self, unsigned short raw_current);
    void (*process_voltage_data)(*********_Device* self);
    void (*process_temperature_data)(*********_Device* self);
} bms_sync;
```

### 4. 观察者模式

**数据同步机制**:
- 硬件数据变化 → 触发BMS数据更新
- 统一的数据更新接口
- 事件驱动的数据处理

## 📈 数据流向

### 完整数据流程

```
硬件传感器 →    采样 → 寄存器数据 → 数据转换 → BMS数据管理器 → 应用层
     ↓           ↓          ↓           ↓            ↓              ↓
   温度/电压/   原始数字值   ROM/RAM    专业算法     统一数据结构    业务逻辑
   电流传感器              寄存器      处理函数     各种Manager
```

### 数据更新时序

```
1. 硬件数据读取
   ├── sh367601b_read_ram()          // 读取RAM寄存器
   ├── sh367601b_read_current()      // 读取电流数据
   └── sh367601b_read_rom()          // 读取ROM配置

2. 数据转换处理
   ├── sh367601b_process_voltage_data()      // 电压数据处理
   ├── sh367601b_process_current_data()      // 电流数据处理
   ├── sh367601b_process_temperature_data()  // 温度数据处理
   ├── sh367601b_process_battery_state_data() // 电池状态处理
   ├── sh367601b_process_charge_discharge_data() // 充放电处理
   ├── sh367601b_process_alarm_data()        // 告警数据处理
   └── sh367601b_process_status_data()       // 状态数据处理

3. BMS数据更新
   └── sh367601b_update_bms_from_ram()       // 统一数据更新入口
```

### 关键数据路径

**电压数据路径**:
```
寄存器值 → Reg_From_Voltage() → 统计计算 → VoltageManager.data
```

**温度数据路径**:
```
ADC原始值 → ntc_calculate_external_temp() → 统计计算 → TemperatureManager.data
```

**SOC计算路径**:
```
电流值 + 时间间隔 → update_soc_simple() → BatteryStateManager.data.soc
```

**循环计数路径**:
```
累计放电量 ÷ 电池额定容量 → ChargeDischargeManager.data.cycle_count
```

## 🔧 扩展性设计

### 1. 新芯片支持

添加新的BMS芯片只需：
1. 创建新的驱动文件（如`sh367602b.h/c`）
2. 实现统一的BMS数据接口
3. 继承`BMS_DataManager`结构

### 2. 新功能模块

添加新的管理器：
1. 在`bms_data.h`中定义新的数据结构和管理器
2. 使用宏定义快速生成初始化代码
3. 在`BMS_DataManager`中集成新管理器

### 3. 算法优化

数据转换算法可独立优化：
1. 修改`sh367601xb_conversion.c`中的算法实现
2. 保持接口不变，确保兼容性
3. 支持参数化配置

## 📊 性能特点

### 1. 内存效率
- **静态分配**：所有管理器静态分配，避免动态内存管理
- **结构紧凑**：数据结构设计紧凑，减少内存占用
- **缓存友好**：相关数据聚集存储，提高缓存命中率

### 2. 执行效率
- **方法指针**：避免大量if-else判断，提高执行效率
- **批量处理**：数据批量更新，减少函数调用开销
- **算法优化**：使用高效的数学算法和查找表

### 3. 实时性
- **中断驱动**：支持中断驱动的数据更新
- **优先级处理**：关键数据优先处理
- **时间戳管理**：精确的时间戳管理

## 🛡️ 可靠性设计

### 1. 错误处理
- **空指针检查**：所有函数都进行空指针检查
- **边界保护**：数据范围检查和边界保护

### 2. 数据完整性
- **校验机制**：关键数据的校验机制
- **异常检测**：异常数据检测和处理
- **恢复机制**：数据异常时的恢复机制

### 3. 调试支持
- **详细日志**：完整的调试日志输出
- **状态监控**：系统状态实时监控
- **数据追踪**：关键数据的变化追踪
