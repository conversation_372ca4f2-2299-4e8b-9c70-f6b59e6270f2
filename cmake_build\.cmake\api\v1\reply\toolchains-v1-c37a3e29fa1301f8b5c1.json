{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {}, "path": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32/bin/tc32-elf-gcc", "version": ""}, "language": "ASM", "sourceFileExtensions": ["s", "S", "asm"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": [], "linkDirectories": [], "linkFrameworkDirectories": [], "linkLibraries": []}, "path": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32/bin/tc32-elf-gcc", "version": "8.1.0"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"implicit": {}, "path": "D:/zip/mingw64/bin/windres.exe"}, "language": "RC", "sourceFileExtensions": ["rc", "RC"]}], "version": {"major": 1, "minor": 0}}