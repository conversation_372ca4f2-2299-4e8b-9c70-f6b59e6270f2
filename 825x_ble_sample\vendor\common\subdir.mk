################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../vendor/common/app_buffer.c \
../vendor/common/app_common.c \
../vendor/common/battery_check.c \
../vendor/common/ble_flash.c \
../vendor/common/blt_fw_sign.c \
../vendor/common/blt_led.c \
../vendor/common/blt_soft_timer.c \
../vendor/common/custom_pair.c \
../vendor/common/flash_fw_check.c \
../vendor/common/flash_prot.c \
../vendor/common/simple_sdp.c \
../vendor/common/tlkapi_debug.c \
../vendor/common/user_config.c 

OBJS += \
./vendor/common/app_buffer.o \
./vendor/common/app_common.o \
./vendor/common/battery_check.o \
./vendor/common/ble_flash.o \
./vendor/common/blt_fw_sign.o \
./vendor/common/blt_led.o \
./vendor/common/blt_soft_timer.o \
./vendor/common/custom_pair.o \
./vendor/common/flash_fw_check.o \
./vendor/common/flash_prot.o \
./vendor/common/simple_sdp.o \
./vendor/common/tlkapi_debug.o \
./vendor/common/user_config.o 


# Each subdirectory must supply rules for building sources it contributes
vendor/common/%.o: ../vendor/common/%.c vendor/common/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: TC32 Compiler'
	tc32-elf-gcc -ffunction-sections -fdata-sections -I"D:\Telink_Project\FN_Project" -I"D:\Telink_Project\FN_Project\vendor\common" -I"D:\Telink_Project\FN_Project\common" -I"D:\Telink_Project\FN_Project\drivers\8258" -I"D:\Telink_Project\FN_Project\vendor\user_app\bms" -I"D:\Telink_Project\FN_Project\vendor\user_app\bms\zhongying" -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -Wall -O2 -fpack-struct -fshort-enums -finline-small-functions -std=gnu99 -fshort-wchar -fms-extensions -c -o"$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


