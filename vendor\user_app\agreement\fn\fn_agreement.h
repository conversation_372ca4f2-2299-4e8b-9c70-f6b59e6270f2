#ifndef _USER_APP_BLE_H_
#define _USER_APP_BLE_H_


/* 小程序协议 */
extern void user_app_ble_read_bmsInfo(void);
extern void user_app_ble_read_cellVoltage(void);
extern void user_app_ble_read_singlePointTemperature(void);
extern void user_app_ble_read_equilibriumState(void);
extern void user_app_ble_read_broadcast(void);
extern void user_app_ble_read_mergeInfo(void);
extern void user_app_ble_read_productsInfo(void);
extern void user_app_ble_read_productsSerialNumber(void);
extern void user_app_ble_read_bmsRTC(void);
extern void user_app_ble_read_alarmParameters(void);
extern void user_app_ble_read_productionInfo(void);
extern void user_app_ble_read_mergeInfo(void);

extern void user_app_ble_set_WorkMode(void);
extern void user_app_ble_set_productsSerialNumber(void);
extern void user_app_ble_set_bmsRTC(void);
extern void user_app_ble_set_alarmParameters(void);
extern void user_app_ble_set_appearanceInfo(void);
extern void user_app_ble_set_debuggingData(void);
extern void user_app_ble_set_mosCtrl(char value);
extern void user_app_ble_set_ctrlBalanced(void);

#endif