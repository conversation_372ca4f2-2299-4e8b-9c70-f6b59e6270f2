# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# compile ASM with C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32/bin/tc32-elf-gcc
# compile C with C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32/bin/tc32-elf-gcc
ASM_DEFINES = 

ASM_INCLUDES = 

ASM_FLAGS = -g -DMCU_STARTUP_8258 -ffunction-sections -fdata-sections -Wall

C_DEFINES = 

C_INCLUDES = 

C_FLAGS = -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall

