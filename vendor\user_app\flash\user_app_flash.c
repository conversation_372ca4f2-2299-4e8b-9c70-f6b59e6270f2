#include "flash.h"
#include "tl_common.h"
 
#define FLASH_SECTOR_SIZE 4096
#define FLASH_START_ADDR  0x40000
#define FLASH_END_ADDR  0x50000
#define FLASH_PAGE_SIZE 256


/* flash写 */
int My_Flash_Write(unsigned int addr, unsigned char *data, unsigned int len) 
{
    /* 参数检查 */
    if (addr < FLASH_START_ADDR || addr + len > FLASH_END_ADDR) {
        return -1; // 地址越界
    }
    if (data == NULL && len != 0) {
        return -1; // 数据指针无效
    }

    // 计算起始扇区地址(向下对齐到FLASH_SECTOR_SIZE)
    unsigned int start_sector_addr = addr - (addr % FLASH_SECTOR_SIZE);
    
    // 计算结束地址
    unsigned int end_addr = addr + len;
    
    // 计算结束扇区地址(向上对齐到FLASH_SECTOR_SIZE)
    unsigned int end_sector_addr = end_addr;
    if (end_addr % FLASH_SECTOR_SIZE != 0) {
        end_sector_addr += FLASH_SECTOR_SIZE - (end_addr % FLASH_SECTOR_SIZE);
    }
    
    // 计算需要擦除的扇区数
    unsigned int sectors_to_erase = (end_sector_addr - start_sector_addr) / FLASH_SECTOR_SIZE;
    
    // 为每个扇区创建备份缓冲区
    unsigned char sector_backup[FLASH_SECTOR_SIZE];
    
    // 处理每个需要擦除的扇区
    for (unsigned int i = 0; i < sectors_to_erase; i++) {
        unsigned int sector_addr = start_sector_addr + i * FLASH_SECTOR_SIZE;
        
        // 1. 备份整个扇区的原始数据
        flash_read_data(sector_addr, FLASH_SECTOR_SIZE, sector_backup);
        
        // 2. 擦除扇区
        flash_erase_sector(sector_addr);
        
        // 3. 恢复不需要修改的数据
//        unsigned int restore_start = sector_addr;
//        unsigned int restore_end = sector_addr + FLASH_SECTOR_SIZE;
        
        // 确定当前扇区中需要保留的区域
        unsigned int keep_start = (addr > sector_addr) ? addr : sector_addr;
        unsigned int keep_end = (end_addr < sector_addr + FLASH_SECTOR_SIZE) ? end_addr : sector_addr + FLASH_SECTOR_SIZE;
        
        // 写入扇区开头到修改区域之前的数据
        if (keep_start > sector_addr) {
            unsigned int restore_len = keep_start - sector_addr;
            flash_write_page(sector_addr, restore_len, sector_backup);
        }
        
        // 写入修改区域之后到扇区末尾的数据
        if (keep_end < sector_addr + FLASH_SECTOR_SIZE) {
            unsigned int restore_offset = keep_end - sector_addr;
            unsigned int restore_len = FLASH_SECTOR_SIZE - restore_offset;
            flash_write_page(keep_end, restore_len, sector_backup + restore_offset);
        }
    }

    // 写入新数据
    unsigned int remaining = len;
    unsigned int current_addr = addr;
    unsigned char *current_data = data;

    while (remaining > 0) {
        unsigned int write_size = (remaining > FLASH_PAGE_SIZE) ? FLASH_PAGE_SIZE : remaining;
        
        flash_write_page(current_addr, write_size, current_data);
        
        current_addr += write_size;
        current_data += write_size;
        remaining -= write_size;
    }

    return 0;
}
