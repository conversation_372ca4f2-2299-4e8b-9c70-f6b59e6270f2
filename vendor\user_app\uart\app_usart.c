/*
 * app_usart.c
 *
 *  Created on: 2023-7-12
 *      Author: lvance
 */



#include "tl_common.h"
#include "drivers.h"
#include "stack/ble/ble.h"
#include "app_usart.h"
#include "../system_main/app_main.h"
#include "../../b85m_ble_sample/app_config.h"
#include "../at_cmd/app_at_cmd.h"
#include "../user_app_main.h"
uart_data_t T_txdata_buf;

tsAppUsartRx_t appUsartRx;

_attribute_data_retention_ u8	module_uart_data_flg;
_attribute_data_retention_ u32 module_wakeup_module_tick;

_attribute_data_retention_  unsigned char 		 	spp_rx_fifo_b[SPP_RXFIFO_SIZE * SPP_RXFIFO_NUM] = {0};
_attribute_data_retention_	my_fifo_t	spp_rx_fifo = {
												SPP_RXFIFO_SIZE,
												SPP_RXFIFO_NUM,
												0,
												0,
												spp_rx_fifo_b,};

_attribute_data_retention_  unsigned char 		 	spp_tx_fifo_b[SPP_TXFIFO_SIZE * SPP_TXFIFO_NUM] = {0};
_attribute_data_retention_	my_fifo_t	spp_tx_fifo = {
												SPP_TXFIFO_SIZE,
												SPP_TXFIFO_NUM,
												0,
												0,
												spp_tx_fifo_b,};



#define UART_TX_BUSY			( (spp_tx_fifo.rptr != spp_tx_fifo.wptr) || uart_tx_is_busy() )
#define UART_RX_BUSY			(spp_rx_fifo.rptr != spp_rx_fifo.wptr)

#define GPIO_WAKEUP_MCU_HIGH				do{gpio_setup_up_down_resistor(MCU_WAKEUP_PIN, PM_PIN_PULLUP_1M); gpio_write(MCU_WAKEUP_PIN, 1);}while(0)
#define GPIO_WAKEUP_MCU_LOW					do{gpio_setup_up_down_resistor(MCU_WAKEUP_PIN, PM_PIN_PULLDOWN_100K); gpio_write(MCU_WAKEUP_PIN, 0);}while(0)


void app_handle_mcu_wakeup_staus(void){
#if BLE_MODULE_INDICATE_DATA_TO_MCU
		if(!UART_TX_BUSY && module_uart_data_flg){
			GPIO_WAKEUP_MCU_LOW;
			module_uart_data_flg = 0;
		}
#endif
}

/**
 * @brief		this function is used to process rx uart data.
 * @param[in]	none
 * @return      0 is ok
 */
 int app_get_uart_busy(void){
	return (UART_TX_BUSY|UART_RX_BUSY);
 }

/**
 * @brief		this function is used to process rx uart data.
 * @param[in]	none
 * @return      0 is ok
 */
int rx_from_uart_cb (void)//UART data send to Master,we will handler the data as CMD or DATA
{
	if(my_fifo_get(&spp_rx_fifo) == 0)
	{
		return 0;
	}

	u8* p = my_fifo_get(&spp_rx_fifo);
	u32 rx_len = p[0]; //usually <= 255 so 1 byte should be sufficient

	if (rx_len)
	{
		Uart.len = rx_len;
		memcpy(Uart.buff, &p[4], Uart.len);
		Uart.flg = true;
		my_fifo_pop(&spp_rx_fifo);
	}

	return 0;
}


/**
 * @brief		this function is used to process tx uart data.
 * @param[in]	none
 * @return      0 is ok
 */
int tx_to_uart_cb (void)
{
	u8 *p = my_fifo_get (&spp_tx_fifo);
	// printf("==============");
	if (p && !uart_tx_is_busy ())
	{
		memcpy(&T_txdata_buf.data, p + 2, p[0]+p[1]*256);
		T_txdata_buf.len = p[0]+p[1]*256 ;


#if (BLE_MODULE_INDICATE_DATA_TO_MCU)
		//If the MCU side is designed to have low power consumption and the module has data to pull up
		//the GPIO_WAKEUP_MCU will only wake up the MCU, then you need to consider whether MCU needs a
		//reply time T from wakeup to a stable receive UART data. If you need a response time of T, ch-
		//ange the following 100US to the actual time required by user.
		if(module_wakeup_module_tick){
			while( !clock_time_exceed(module_wakeup_module_tick, 100) );
		}
#endif


		// printf("uart send data len:%d ", T_txdata_buf.len);
		// for (u8 i = 0; i < T_txdata_buf.len; i++)
		// 	printf("%02x ", T_txdata_buf.data[i]);
		if (uart_dma_send((u8 *)(&T_txdata_buf)))
		{
			my_fifo_pop (&spp_tx_fifo);
		}
	}
	return 0;
}


/**
 * @brief		this function is used to process uart rx interrupt.
 * @param[in]	none
 * @return      none
 */
void app_usart_irq_pro(void){
    unsigned char irqS = dma_chn_irq_status_get();
    if(irqS & FLD_DMA_CHN_UART_RX)	//rx
    {
    	dma_chn_irq_status_clr(FLD_DMA_CHN_UART_RX);

    	u8* w = spp_rx_fifo.p + (spp_rx_fifo.wptr & (spp_rx_fifo.num-1)) * spp_rx_fifo.size;
    	if(w[0] != 0 || w[1] != 0)
    	{
    		my_fifo_next(&spp_rx_fifo);
    		u8* p = spp_rx_fifo.p + (spp_rx_fifo.wptr & (spp_rx_fifo.num-1)) * spp_rx_fifo.size;
    		reg_dma_uart_rx_addr = (u16)((u32)p);  //switch uart RX dma address
    	}
    }

    if(irqS & FLD_DMA_CHN_UART_TX)	//tx
    {
    	dma_chn_irq_status_clr(FLD_DMA_CHN_UART_TX);
    }
}

/**
 * @brief		this function is used to process tx uart data.
 * @param[in]	none
 * @return      0 is ok
 */
int app_usart_fifo_push(unsigned char  *p_buf ,unsigned char len )
{
#if (BLE_MODULE_INDICATE_DATA_TO_MCU)
	if(!module_uart_data_flg){ //UART idle, new data is sent
		GPIO_WAKEUP_MCU_HIGH;  //Notify MCU that there is data here
		module_wakeup_module_tick = clock_time() | 1;
		module_uart_data_flg = 1;
		//printf("GPIO_WAKEUP_MCU_LEVEL=%d\r\n",gpio_read(GPIO_PB6));
	}
#endif
	if(my_fifo_push(&spp_tx_fifo,p_buf,len)==0){
		return 1;
	}else{
		return 0;
	}
}


/**
 * void app_usart_int(unsigned int baudRate, unsigned char wakeupMode);
 *
 * baudRate:波特率
 *
 * wakeupMode 启动模式初始化 0-powerup 启动；1-sleep唤醒
 *
*/
void app_usart_int(unsigned int baudRate, unsigned char wakeupMode){
	// printf("baudrate:%d", baudRate);
    if(!wakeupMode){
        	//note: dma addr must be set first before any other uart initialization!
        u8 *uart_rx_addr = (spp_rx_fifo_b + (spp_rx_fifo.wptr & (spp_rx_fifo.num-1)) * spp_rx_fifo.size);
        uart_recbuff_init( (unsigned char *)uart_rx_addr, spp_rx_fifo.size);

        uart_gpio_set(UART_TX_PIN, UART_RX_PIN);


        uart_reset();  //will reset uart digital registers from 0x90 ~ 0x9f, so uart setting must set after this reset

        //baud rate: 115200
        uart_init_baudrate(baudRate,CLOCK_SYS_CLOCK_HZ,PARITY_NONE,STOP_BIT_ONE);

        uart_dma_enable(1, 1); 	//uart data in hardware buffer moved by dma, so we need enable them first

        irq_set_mask(FLD_IRQ_DMA_EN);
        dma_chn_irq_enable(FLD_DMA_CHN_UART_RX | FLD_DMA_CHN_UART_TX, 1);   	//uart Rx/Tx dma irq enable

        uart_irq_enable(0, 0);  	//uart Rx/Tx irq no need, disable them

        blc_register_hci_handler(rx_from_uart_cb, tx_to_uart_cb);				//customized uart handler


    }else{
                //note: dma addr must be set first before any other uart initialization!

        u8 *uart_rx_addr = (spp_rx_fifo_b + (spp_rx_fifo.wptr & (spp_rx_fifo.num-1)) * spp_rx_fifo.size);
        uart_recbuff_init( (unsigned char *)uart_rx_addr, spp_rx_fifo.size);



        uart_gpio_set(UART_TX_PIN, UART_RX_PIN);

        uart_reset();  //will reset uart digital registers from 0x90 ~ 0x9f, so uart setting must set after this reset



        uart_init_baudrate(baudRate,CLOCK_SYS_CLOCK_HZ,PARITY_NONE,STOP_BIT_ONE);

        uart_dma_enable(1, 1); 	//uart data in hardware buffer moved by dma, so we need enable them first

        irq_set_mask(FLD_IRQ_DMA_EN);
        dma_chn_irq_enable(FLD_DMA_CHN_UART_RX | FLD_DMA_CHN_UART_TX, 1);   	//uart Rx/Tx dma irq enable

        uart_irq_enable(0, 0);  	//uart Rx/Tx irq no need, disable them
    }

	#if BLE_MODULE_INDICATE_DATA_TO_MCU
	gpio_set_func(MCU_WAKEUP_PIN, AS_GPIO);
	gpio_set_input_en(MCU_WAKEUP_PIN, 0);
	gpio_set_output_en(MCU_WAKEUP_PIN, 1);
	gpio_setup_up_down_resistor(MCU_WAKEUP_PIN, PM_PIN_PULLDOWN_100K);
	gpio_write(MCU_WAKEUP_PIN, 0);
	#endif
}


/**
 * @brief		this callback function is used to reinitialize uart.
 * @param[in]	none
 * @return      -1 is ok and delete the  sotfware timer
 */
int app_reinit_uartCB(int brate)
{
        //baud rate: 115200
	uart_init_baudrate(brate,CLOCK_SYS_CLOCK_HZ,PARITY_NONE,STOP_BIT_ONE);

	uart_dma_enable(1, 1); 	//uart data in hardware buffer moved by dma, so we need enable them first

	irq_set_mask(FLD_IRQ_DMA_EN);
	dma_chn_irq_enable(FLD_DMA_CHN_UART_RX | FLD_DMA_CHN_UART_TX, 1);   	//uart Rx/Tx dma irq enable

	uart_irq_enable(0, 0);  	//uart Rx/Tx irq no need, disable them
	uart_mask_error_irq_enable();

	extern int rx_from_uart_cb (void);
	extern int tx_to_uart_cb (void);
	blc_register_hci_handler(rx_from_uart_cb, tx_to_uart_cb);

	return -1;
}


