#ifndef __LIST_H__
#define __LIST_H__

// #include <stddef.h>  // for offsetof

// 链表节点
struct list_head {
    struct list_head *next, *prev;
};

// 初始化链表头
#define LIST_HEAD_INIT(name) { &(name), &(name) }
#define LIST_HEAD(name) struct list_head name = LIST_HEAD_INIT(name)

// 初始化节点
static inline void INIT_LIST_HEAD(struct list_head *list) {
    list->next = list;
    list->prev = list;
}

// 插入节点（内部使用）
static inline void __list_add(struct list_head *new_item, struct list_head *prev, struct list_head *next) {
    next->prev = new_item;
    new_item->next = next;
    new_item->prev = prev;
    prev->next = new_item;
}

// 头插法
static inline void list_add(struct list_head *new_item, struct list_head *head) {
    __list_add(new_item, head, head->next);
}

// 尾插法
static inline void list_add_tail(struct list_head *new_item, struct list_head *head) {
    __list_add(new_item, head->prev, head);
}

// 删除节点（内部使用）
static inline void __list_del(struct list_head *prev, struct list_head *next) {
    next->prev = prev;
    prev->next = next;
}

// 删除节点
static inline void list_del(struct list_head *entry) {
    __list_del(entry->prev, entry->next);
    entry->next = 0;
    entry->prev = 0;
}

// 判断链表是否为空
static inline int list_empty(const struct list_head *head) {
    return head->next == head;
}

// 获取宿主结构体
#define container_of(ptr, type, member) \
    ((type *)((char *)(ptr) - offsetof(type, member)))

// 遍历链表
#define list_for_each(pos, head) \
    for (pos = (head)->next; pos != (head); pos = pos->next)

#endif // __LIST_H__
