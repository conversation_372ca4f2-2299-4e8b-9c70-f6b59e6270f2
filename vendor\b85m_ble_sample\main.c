/********************************************************************************************************
 * @file    main.c
 *
 * @brief   This is the source file for BLE SDK
 *
 * <AUTHOR> GROUP
 * @date    06,2020
 *
 * @par     Copyright (c) 2020, Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *
 *******************************************************************************************************/
#include "tl_common.h"
#include "drivers.h"
#include "stack/ble/ble.h"
#include "app.h"
#include "../user_app/system_main/app_main.h"
#include "../user_app/uart/app_usart.h"
#include "../user_app/user_app_main.h"

/* printf引脚初始化 */
void User_App_Debug_Init(void)
{
	gpio_set_func(GPIO_PA7, AS_GPIO); 		/* 数字gpio */
	gpio_set_output_en(GPIO_PA7, 1);		/* 使能输出 */
	gpio_set_input_en(GPIO_PA7, 0); 		/* 禁用输入 */
	gpio_write(GPIO_PA7, 1); 				/* 输出高 */
}

/**
 * @brief   IRQ handler
 * @param   none.
 * @return  none.
 */
_attribute_ram_code_ void irq_handler(void)
{
	irq_blt_sdk_handler();

	app_usart_irq_pro();

	if(timer_get_interrupt_status(FLD_TMR_STA_TMR0))
	{
		timer_clear_interrupt_status(FLD_TMR_STA_TMR0);
		time0_callback();
	}
	if(timer_get_interrupt_status(FLD_TMR_STA_TMR1))
	{
		timer_clear_interrupt_status(FLD_TMR_STA_TMR1);
		time1_callback();
	}
}


/**
 * @brief		This is main function
 * @param[in]	none
 * @return      none
 */
_attribute_ram_code_ int main (void)    //must run in ramcode
{

	DBG_CHN0_LOW;   //debug

	blc_pm_select_internal_32k_crystal();

	#if(MCU_CORE_TYPE == MCU_CORE_825x)
		cpu_wakeup_init();
	#elif(MCU_CORE_TYPE == MCU_CORE_827x)
		cpu_wakeup_init(LDO_MODE,EXTERNAL_XTAL_24M);
	#endif

	int deepRetWakeUp = pm_is_MCU_deepRetentionWakeup();  //MCU deep retention wakeUp

	rf_drv_ble_init();

	gpio_init(!deepRetWakeUp);  //analog resistance will keep available in deepSleep mode, so no need initialize again

	
	
	clock_init(SYS_CLK_TYPE);

	if( deepRetWakeUp ){
		user_init_deepRetn();
	}
	else{
		user_init_normal();
	}

	User_App_Debug_Init();
	User_App_Init();

	
#if CUSTOMER_CODE_ENABLE
	appInit(deepRetWakeUp);
#endif
#if BLT_SOFTWARE_TIMER_ENABLE
	blt_soft_timer_init();
#endif

	
    irq_enable();
	// blt_soft_timer_add(user_app_chip_write, 1000 * 100);
	while (1) {
#if (MODULE_WATCHDOG_ENABLE)
		wd_clear(); //clear watch dog
#endif
		main_loop();
	}
}

