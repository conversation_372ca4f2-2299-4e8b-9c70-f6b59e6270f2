/*
 * user_main.h
 *
 *  Created on: 2024-4-18
 *      Author: lvance
 */

#ifndef USER_MAIN_H_
#define USER_MAIN_H_

#include "../../b85m_ble_sample/app_config.h"

#if UART_PRINT_DEBUG_ENABLE
#define  LOG_DEBUG(fmt, ... )  printf("""[USER]LOG]:"fmt"",##__VA_ARGS__);
#else
#define  LOG_DEBUG(fmt, ... )  (void)0
#endif

#define CUSTOMER_CODE_ENABLE    1

#define SW_VER			0x0003
#define ModuleNme		"YC0001"

#define SYSTEM_PARA_ADDR_BASE  0x79000
#define default_name    {"LTW-YC38"}
#define default_data  	{0}//  {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00}

#define write_flag		0x5CA5







typedef enum
{
	BRATE_9600,
	BRATE_19200,
	BRATE_38400,
	BRATE_115200,
	BRATE_230400,
	BRATE_380400
}teBrate_t;

/*默认参数赋值*/
#define Parameter_init_default  \
{                               \
	write_flag,                 /*写标志*/ \
    9600,               		/*波特率*/            \
	0,                          /*功率索引*/    \
    480,                        /*最小广播间隔 单位：0.625ms*/            \
    488,                        /*最大广播间隔 单位：0.625ms*/\
    32,                         /*最小连接间隔 单位1.25ms*/ \
    40,                         /*最大连接间隔 单位1.25ms*/ \
    12,                         /*广播名称长度*/\
    default_name,               /*广播名称*/ \
    0,                          /*自定义广播数据长度*/\
    default_data,               /*自定义广播数据内容*/ \
	0,							/*<鉴权标志>*/\
	123456,						/*<鉴权密码>*/\
}


/*******************************************************************************************************
 * flashBuffer[54]数据结构
 * byte0-1:writeFlag
 * byte2:brate
 * byte3-4:advIntervalMin
 * byte5-6:advIntervalMax
 * byte7-8:conIntervalMin
 * byte9-10:conIntervalMax
 * byte11:nameLen
 * byte12-31:name[]
 * byte32:advLen
 * byte33-52:advData[]
 *******************************************************************************************************/


typedef struct //__attribute__((aligned(1)))
{
    /* data */
    u16 writeFlag;
    u32 brate;
	u8  txPowerIndex;
	u16 advIntervalMin;
	u16 advIntervalMax;
	u16 conIntervalMin;
	u16 conIntervalMax;
	u8  nameLen;
	u8  name[20];
	u8  advLen;
	u8	advData[20];
	u8  authEnable;
	u32  authWord;
}tsParaToFlash_t;

extern tsParaToFlash_t paraToFlash;
extern u8 softwareVer[18];
extern u8 blemac[6];
int app_requestNewConnParameterCb(void);

void appSystemParamaterInit(void);
void appSystemParamaterSave(void);
u8 appGetBusyStatus(void);
void appInit(unsigned int setupMode);
void appMainProc(void);


#endif /* USER_MAIN_H_ */
