#include "queue.h"

void queue_init(Queue *q) {
    q->front = 0;
    q->rear = 0;
    q->count = 0;
}


int queue_push(Queue *q, uint8_t data, uint8_t time) {
    if (q->count >= QUEUE_SIZE) {
        return -1;  // 队列满
    }
    q->nodes[q->rear].data = data;
    q->nodes[q->rear].time = time;
    q->rear = (q->rear + 1) % QUEUE_SIZE;
    q->count++;
    return 0;
}

int queue_pop(Queue *q, QueueNode *node) {
    if (q->count == 0) {
        return -1;  // 队列空
    }
    node->data = q->nodes[q->front].data;
    node->time = q->nodes[q->front].time;
    q->front = (q->front + 1) % QUEUE_SIZE;
    q->count--;
    return 0;
}

int queue_empty(Queue *q) {
    return (q->count == 0);
}
