/********************************************************************************************************
 * @file	driver.h
 *
 * @brief	This is the header file for B87
 *
 * <AUTHOR> Group
 * @date	2019
 *
 * @par     Copyright (c) 2019, Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *
 *******************************************************************************************************/
#pragma once



#include "lib/include/aoa.h"
#include "emi.h"
#include "lib/include/pm.h"
#include "lib/include/random.h"
#include "lib/include/rf_drv.h"

#include "lib/include/pke/pke.h"
#include "lib/include/pke/pke_algorithm.h"

#include "bsp.h"
#include "aes.h"
#include "analog.h"
#include "compiler.h"
#include "register.h"
#include "gpio.h"
#include "gpio_default.h"
#include "pwm.h"
#include "irq.h"
#include "clock.h"
#include "flash.h"
#include "audio.h"
#include "adc.h"
#include "i2c.h"
#include "spi.h"
#include "uart.h"
#include "register.h"
#include "watchdog.h"
#include "register.h"
#include "dfifo.h"
#include "dma.h"
#include "timer.h"

#include "s7816.h"
#include "qdec.h"
#include "lpc.h"

#include "usbhw.h"
#include "drivers/8278/driver_ext/rf_pa.h"

#include "flash/flash_type.h"
#include "sdk_version.h"

