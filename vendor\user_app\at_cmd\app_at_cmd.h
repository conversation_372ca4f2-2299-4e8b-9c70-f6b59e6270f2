/*
 * app_at_cmd.h
 *
 *  Created on: 2024-4-18
 *      Author: lvance
 */

#ifndef APP_AT_CMD_H_
#define APP_AT_CMD_H_

typedef enum{
	AT_REMAIN_RSTN=0x01,
	AT_REMAIN_RSTORE=0x02,
	AT_REMAIN_BRATE=0x04,
}teAtCmdRemainTime_t;


typedef enum{
	AT_GATT,
	AT_UART
}tsAtSource_t;

typedef enum{
    AT=0,
    AT_VER,
    AT_RSTN,
    AT_RSTORE,
    AT_BRATE,
    AT_AUTH,
    AT_MAC,
    AT_POWER,
    AT_ADVNAME,
    AT_ADVDATA,
    AT_ADVINT,
    AT_CONPARA,

    AT_INLEGAL_CMD
    
}tsAtIndex_t;


typedef struct{
	teAtCmdRemainTime_t cmd;
	u32 tick[3];
}tsAtRemainTime_t;

typedef struct{
    int length;
    unsigned char data[255];

}tsAtResponse_t;

int appUartOrGattReceiveHandle(unsigned char *rxData, int rxLength,tsAtSource_t source);
//int appGattReceiveHandle(unsigned char *rxData, int rxLength);
void appAtCmdRemainProc(void);

#endif /* APP_AT_CMD_H_ */
