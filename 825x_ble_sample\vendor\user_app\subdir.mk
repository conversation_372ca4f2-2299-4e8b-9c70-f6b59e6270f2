################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../vendor/user_app/app_at_cmd.c \
../vendor/user_app/app_ble.c \
../vendor/user_app/app_mian.c \
../vendor/user_app/app_usart.c \
../vendor/user_app/user_app_ble.c \
../vendor/user_app/user_app_conversion.c \
../vendor/user_app/user_app_flash.c \
../vendor/user_app/user_app_main.c \
../vendor/user_app/user_app_mymethod.c \
../vendor/user_app/user_app_rs2058.c \
../vendor/user_app/user_app_sh367601b.c 

OBJS += \
./vendor/user_app/app_at_cmd.o \
./vendor/user_app/app_ble.o \
./vendor/user_app/app_mian.o \
./vendor/user_app/app_usart.o \
./vendor/user_app/user_app_ble.o \
./vendor/user_app/user_app_conversion.o \
./vendor/user_app/user_app_flash.o \
./vendor/user_app/user_app_main.o \
./vendor/user_app/user_app_mymethod.o \
./vendor/user_app/user_app_rs2058.o \
./vendor/user_app/user_app_sh367601b.o 


# Each subdirectory must supply rules for building sources it contributes
vendor/user_app/%.o: ../vendor/user_app/%.c vendor/user_app/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: TC32 Compiler'
	tc32-elf-gcc -ffunction-sections -fdata-sections -I"D:\Telink_Project\FN_Project" -I"D:\Telink_Project\FN_Project\vendor\common" -I"D:\Telink_Project\FN_Project\common" -I"D:\Telink_Project\FN_Project\drivers\8258" -I"D:\Telink_Project\FN_Project\vendor\user_app\bms" -I"D:\Telink_Project\FN_Project\vendor\user_app\bms\zhongying" -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -Wall -O2 -fpack-struct -fshort-enums -finline-small-functions -std=gnu99 -fshort-wchar -fms-extensions -c -o"$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


