/********************************************************************************************************
 * @file    boot.link
 *
 * @brief   This is the link file for B85m
 *
 * <AUTHOR> Group
 * @date    2022
 *
 * @par     Copyright (c) 2022, Telink Semiconductor (Shanghai) Co., Ltd. ("TELINK")
 *
 *          Licensed under the Apache License, Version 2.0 (the "License");
 *          you may not use this file except in compliance with the License.
 *          You may obtain a copy of the License at
 *
 *              http://www.apache.org/licenses/LICENSE-2.0
 *
 *          Unless required by applicable law or agreed to in writing, software
 *          distributed under the License is distributed on an "AS IS" BASIS,
 *          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *          See the License for the specific language governing permissions and
 *          limitations under the License.
 *
 *******************************************************************************************************/
/* to tell the linker the program begin from __start label in cstartup.s, thus do not treat it as a unused symbol */
ENTRY(__start)

SECTIONS
{
    . = 0x0;
    .vectors :
    {
    *(.vectors)
    *(.vectors.*)	/* MUST as follows, when compile with -ffunction-sections -fdata-sections, session name may changed */
    }
    .ram_code :
    {
    *(.ram_code)
    *(.ram_code.*)
    }
    
    . = (((. + 3) / 4)*4);
        PROVIDE(_rstored_ = . );
    
	PROVIDE(_ramcode_size_ = . );


    . = (0x840000 + (_rstored_));
	    .retention_data :
          AT (_rstored_)
        {
        . = (((. + 3) / 4)*4);
        PROVIDE(_retention_data_start_ = . );
        *(.retention_data)
        *(.retention_data.*)
        PROVIDE(_retention_data_end_ = . );
        }
	    
	PROVIDE(_retention_data_size_div_256_ = (. - 0x840000 + 255) / 256);
	PROVIDE(_retention_data_size_align_256_ = ( (. - 0x840000 + 255) / 256) * 256);
	
	
	. = _retention_data_size_align_256_;
	.text :
	    {
	    *(.text)
	    *(.text.*)
	    }
	.rodata :
	    {
	    *(.rodata)
	    *(.rodata.*)
	    }
	
	. = (((. + 3) / 4)*4);
		PROVIDE(_dstored_ = . );
	    PROVIDE(_code_size_ = .);
		 
		 
    . = (0x840900 + (_retention_data_size_align_256_));
	    .data :
	      AT ( _dstored_ )
	     {
	. = (((. + 3) / 4)*4);
	     PROVIDE(_start_data_ = . );
	     *(.data);
	     *(.data.*);
	. = (((. + 3) / 4)*4);
	     PROVIDE(_end_data_ = . );
	     } 
	PROVIDE(_sstored_ = _dstored_ + _end_data_ - _start_data_);


	/* irk_stk is 4 word align */
	. = (((. + 3) / 4)*4);
		PROVIDE(_start_bss_ = .);
	    .bss(NOLOAD) :
	    {
	    *(.sbss)
	    *(.sbss.*)
	    *(.bss)
	    *(.bss.*)
	    }
		PROVIDE(_end_bss_ = .);
		
		/* data in ram but no need to clean in .s*/
        .data_no_init (NOLOAD) :
        {
    . = (((. + 3) / 4)*4);
            *(.data_no_init)
            *(.data_no_init.*)
        }
		PROVIDE(_ram_use_end_ = .);
		
	    .sdk_version :
	    AT ( _sstored_ )
		{
	. = (((. + 3) / 4)*4);
		KEEP(*(.sdk_version));
	    KEEP(*(.sdk_version.*));
		}
		
		PROVIDE(_bin_size_ = _code_size_ + _end_data_ - _start_data_  + SIZEOF(.sdk_version) );
		PROVIDE(_ictag_start_ = 0x840000 + (_retention_data_size_div_256_) * 0x100);
		PROVIDE(_ictag_end_ = 0x840000 + (_retention_data_size_div_256_ + 1) * 0x100);
		
		PROVIDE(_retention_use_size_div_16_ = (_retention_data_end_ - 0x840000  + 15 ) / 16);
		PROVIDE(_retention_size_ = _retention_use_size_div_16_ * 16);
}
ASSERT(_retention_size_ * __PM_DEEPSLEEP_RETENTION_ENABLE < 0x8000, "Error: Retention RAM size overflow.");
ASSERT(_ram_use_end_<(__SRAM_SIZE - 600), "Error: RAM size maybe overflow.");/*600byte is the stack size used when secure connect is not enabled.*/