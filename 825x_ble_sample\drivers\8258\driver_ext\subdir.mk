################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../drivers/8258/driver_ext/ext_calibration.c \
../drivers/8258/driver_ext/ext_misc.c \
../drivers/8258/driver_ext/rf_pa.c \
../drivers/8258/driver_ext/software_uart.c 

OBJS += \
./drivers/8258/driver_ext/ext_calibration.o \
./drivers/8258/driver_ext/ext_misc.o \
./drivers/8258/driver_ext/rf_pa.o \
./drivers/8258/driver_ext/software_uart.o 


# Each subdirectory must supply rules for building sources it contributes
drivers/8258/driver_ext/%.o: ../drivers/8258/driver_ext/%.c drivers/8258/driver_ext/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: TC32 Compiler'
	tc32-elf-gcc -ffunction-sections -fdata-sections -I"D:\Telink_Project\FN_Project" -I"D:\Telink_Project\FN_Project\vendor\common" -I"D:\Telink_Project\FN_Project\common" -I"D:\Telink_Project\FN_Project\drivers\8258" -I"D:\Telink_Project\FN_Project\vendor\user_app\bms" -I"D:\Telink_Project\FN_Project\vendor\user_app\bms\zhongying" -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -Wall -O2 -fpack-struct -fshort-enums -finline-small-functions -std=gnu99 -fshort-wchar -fms-extensions -c -o"$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


