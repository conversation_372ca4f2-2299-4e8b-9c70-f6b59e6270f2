#include "user_app_main.h"

#define USER_APP_RS2058_IO1 GPIO_PB6
#define USER_APP_RS2058_IO2 GPIO_PB7

/* 初始化引脚RS2058 */
void User_App_Rs2058_Init(void)
{
    gpio_set_func(USER_APP_RS2058_IO1, AS_GPIO); 		/* 数字gpio */
	gpio_set_output_en(USER_APP_RS2058_IO1, 1);		/* 使能输出 */
	gpio_set_input_en(USER_APP_RS2058_IO1, 0); 		/* 禁用输入 */

    // gpio_set_func(USER_APP_RS2058_IO2, AS_GPIO); 		/* 数字gpio */
	// gpio_set_output_en(USER_APP_RS2058_IO2, 1);		/* 使能输出 */
	// gpio_set_input_en(USER_APP_RS2058_IO2, 0); 		/* 禁用输入 */
	User_App_Rs2058_Gpio1_Low();
    // user_app_rs2058_gpio2_hight();
	// gpio_write(USER_APP_RS2058_IO1_2, 1); 				/* 输出高 */
}

/* 拉高 */
void User_App_Rs2058_Gpio1_Hight(void)
{
    gpio_write(USER_APP_RS2058_IO1, 1); 				/* 输出高 */
	sleep_ms(10);
}
/* 拉低 */
void User_App_Rs2058_Gpio1_Low(void)
{
    gpio_write(USER_APP_RS2058_IO1, 0); 				/* 输出低 */
	sleep_ms(10);
}
