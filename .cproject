<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="com.telink.tc32eclipse.configuration.app.debug.**********.351063629.799040038.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.telink.tc32eclipse.configuration.app.debug.**********.351063629.799040038.**********" moduleId="org.eclipse.cdt.core.settings" name="825x_ble_sample">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.MakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildArtefactType="com.telink.tc32eclipse.buildArtefactType.app" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=com.telink.tc32eclipse.buildArtefactType.app,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" description="" errorParsers="org.eclipse.cdt.core.MakeErrorParser;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser" id="com.telink.tc32eclipse.configuration.app.debug.**********.351063629.799040038.**********" name="825x_ble_sample" optionalBuildProperties="org.eclipse.cdt.docker.launcher.containerbuild.property.selectedvolumes=,org.eclipse.cdt.docker.launcher.containerbuild.property.volumes=" parent="com.telink.tc32eclipse.configuration.app.debug" postannouncebuildStep="" postbuildStep="&quot;${workspace_loc:/${ProjName}}/tl_check_fw.sh&quot;   ${ConfigName}   ${ProjName}" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="com.telink.tc32eclipse.configuration.app.debug.**********.351063629.799040038.**********." name="/" resourcePath="">
						<toolChain errorParsers="" id="com.telink.tc32eclipse.toolchain.TC32Win.app.debug.1517056655" name="TC32-GCC Toolchain" superClass="com.telink.tc32eclipse.toolchain.TC32Win.app.debug">
							<option id="com.telink.tc32eclipse.toolchain.options.toolchain.objcopy.flash.app.debug.655596531" name="Generate binary file for Flash memory" superClass="com.telink.tc32eclipse.toolchain.options.toolchain.objcopy.flash.app.debug" useByScannerDiscovery="false"/>
							<option id="com.telink.tc32eclipse.toolchain.options.toolchain.objdump.app.debug.1551879731" name="Generate Extended Listing (Source + generated Assembler)" superClass="com.telink.tc32eclipse.toolchain.options.toolchain.objdump.app.debug" useByScannerDiscovery="false"/>
							<option id="com.telink.tc32eclipse.toolchain.options.toolchain.size.app.debug.1334956670" name="Print Size" superClass="com.telink.tc32eclipse.toolchain.options.toolchain.size.app.debug" useByScannerDiscovery="false"/>
							<targetPlatform binaryParser="org.eclipse.cdt.core.ELF" id="com.telink.tc32eclipse.targetplatform.TC32Win.app.debug.935308449" name="TC32 Cross-Target" superClass="com.telink.tc32eclipse.targetplatform.TC32Win.app.debug"/>
							<builder buildPath="${workspace_loc:/ble_lt_ms/Debug}" errorParsers="org.eclipse.cdt.core.MakeErrorParser" id="com.telink.tc32eclipse.target.builder.TC32Win.app.debug.412045270" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="GNU Make Builder" superClass="com.telink.tc32eclipse.target.builder.TC32Win.app.debug"/>
							<tool command="tc32-elf-gcc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG}${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GASErrorParser" id="com.telink.tc32eclipse.tool.assembler.TC32Win.app.debug.1741657746" name="TC32 CC/Assembler" superClass="com.telink.tc32eclipse.tool.assembler.TC32Win.app.debug">
								<option id="com.telink.tc32eclipse.assembler.option.debug.level.186643336" name="Generate Debugging Info" superClass="com.telink.tc32eclipse.assembler.option.debug.level" useByScannerDiscovery="false" value="com.telink.tc32eclipse.assembler.option.debug.level.none" valueType="enumerated"/>
								<option id="com.telink.tc32eclipse.asm.option.flags.960620651" name="Other GCC Flags" superClass="com.telink.tc32eclipse.asm.option.flags" useByScannerDiscovery="false" value="-DMCU_STARTUP_8258" valueType="string"/>
								<inputType id="com.telink.tc32eclipse.tool.assembler.input.1912949102" superClass="com.telink.tc32eclipse.tool.assembler.input"/>
							</tool>
							<tool command="tc32-elf-gcc -ffunction-sections -fdata-sections" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG}${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="com.telink.tc32eclipse.tool.compiler.TC32Win.app.debug.**********" name="TC32 Compiler" superClass="com.telink.tc32eclipse.tool.compiler.TC32Win.app.debug">
								<option id="com.telink.tc32eclipse.compiler.option.debug.level.798297867" name="Generate Debugging Info" superClass="com.telink.tc32eclipse.compiler.option.debug.level" useByScannerDiscovery="false" value="com.telink.tc32eclipse.compiler.option.debug.level.none" valueType="enumerated"/>
								<option id="com.telink.tc32eclipse.compiler.option.optimize.1565800147" name="Optimization Level" superClass="com.telink.tc32eclipse.compiler.option.optimize" useByScannerDiscovery="false" value="com.telink.tc32eclipse.compiler.optimize.two" valueType="enumerated"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.telink.tc32eclipse.compiler.option.incpath.1778665714" name="Include Paths (-I)" superClass="com.telink.tc32eclipse.compiler.option.incpath" useByScannerDiscovery="false" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/vendor/common}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/common}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/drivers/8258}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/vendor/user_app/bms}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/vendor/user_app/bms/zhongying}&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.telink.tc32eclipse.compiler.option.def.807317074" name="Define Syms (-D)" superClass="com.telink.tc32eclipse.compiler.option.def" useByScannerDiscovery="false" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="__PROJECT_8258_BLE_SAMPLE__=1"/>
									<listOptionValue builtIn="false" value="CHIP_TYPE=CHIP_TYPE_825x"/>
								</option>
								<option id="com.telink.tc32eclipse.compiler.option.std.463849061" name="Language Standard" superClass="com.telink.tc32eclipse.compiler.option.std" useByScannerDiscovery="false" value="com.telink.tc32eclipse.compiler.option.std.gnu99" valueType="enumerated"/>
								<option id="com.telink.tc32eclipse.compiler.option.otherflags.1825715216" name="Other flags" superClass="com.telink.tc32eclipse.compiler.option.otherflags" useByScannerDiscovery="false" value="-fshort-wchar -fms-extensions" valueType="string"/>
								<option id="com.telink.tc32eclipse.compiler.option.optimize.packstruct.561071008" name="Pack structs (-fpack-struct)" superClass="com.telink.tc32eclipse.compiler.option.optimize.packstruct" useByScannerDiscovery="false" value="true" valueType="boolean"/>
								<option id="com.telink.tc32eclipse.compiler.option.optimize.shortenums.10321961" name="Short enums (-fshort-enums)" superClass="com.telink.tc32eclipse.compiler.option.optimize.shortenums" useByScannerDiscovery="false" value="true" valueType="boolean"/>
								<option id="com.telink.tc32eclipse.compiler.option.optimize.other.921359702" name="Other Optimization Flags" superClass="com.telink.tc32eclipse.compiler.option.optimize.other" useByScannerDiscovery="false" value="-finline-small-functions" valueType="string"/>
								<inputType id="com.telink.tc32eclipse.compiler.TC32Win.input.**********" name="C Source Files" superClass="com.telink.tc32eclipse.compiler.TC32Win.input"/>
							</tool>
							<tool command="tc32-elf-ld --gc-sections" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG}${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="com.telink.tc32eclipse.tool.linker.TC32Win.app.debug.507976582" name="TC32 C Linker" superClass="com.telink.tc32eclipse.tool.linker.TC32Win.app.debug">
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.telink.tc32eclipse.linker.option.libpath.1551654035" name="Libraries Path (-L)" superClass="com.telink.tc32eclipse.linker.option.libpath" useByScannerDiscovery="false" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/proj_lib}&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.telink.tc32eclipse.linker.option.libs.1210791863" name="Libraries (-l)" superClass="com.telink.tc32eclipse.linker.option.libs" useByScannerDiscovery="false" valueType="libs">
									<listOptionValue builtIn="false" value="lt_825x"/>
									<listOptionValue builtIn="false" value="soft-fp"/>
									<listOptionValue builtIn="false" value="lt_general_stack"/>
								</option>
								<inputType id="com.telink.tc32eclipse.tool.linker.input.318733766" name="OBJ Files" superClass="com.telink.tc32eclipse.tool.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="com.telink.tc32eclipse.tool.archiver.TC32Win.base.107493748" name="TC32 Archiver" superClass="com.telink.tc32eclipse.tool.archiver.TC32Win.base"/>
							<tool command="tc32-elf-objdump" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} &gt;${OUTPUT}" errorParsers="" id="com.telink.tc32eclipse.tool.objdump.TC32Win.app.debug.1000861688" name="TC32 Create Extended Listing" superClass="com.telink.tc32eclipse.tool.objdump.TC32Win.app.debug">
								<option id="com.telink.tc32eclipse.objdump.option.output.1663935315" name="Output Filename" superClass="com.telink.tc32eclipse.objdump.option.output" useByScannerDiscovery="false" value="825x_ble_sample.lst" valueType="string"/>
								<option id="com.telink.tc32eclipse.objdump.option.stdopts.730958911" name="Options to create Extended Listing" superClass="com.telink.tc32eclipse.objdump.option.stdopts" useByScannerDiscovery="false" value="-x -l -S" valueType="string"/>
							</tool>
							<tool command="tc32-elf-objcopy" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS} ${OUTPUT}" errorParsers="" id="com.telink.tc32eclipse.tool.objcopy.flash.TC32Win.app.debug.584073364" name="TC32 Create Flash image" superClass="com.telink.tc32eclipse.tool.objcopy.flash.TC32Win.app.debug">
								<option id="com.telink.tc32eclipse.objcopy.flash.option.output.2077320026" name="Output Filename" superClass="com.telink.tc32eclipse.objcopy.flash.option.output" useByScannerDiscovery="false" value="" valueType="string"/>
								<option id="com.telink.tc32eclipse.objcopy.option.flash.stdopts.311433774" name="Options to create flash image" superClass="com.telink.tc32eclipse.objcopy.option.flash.stdopts" useByScannerDiscovery="false" value="" valueType="string"/>
							</tool>
							<tool command="tc32-elf-size" commandLinePattern="${COMMAND} ${FLAGS} ${INPUTS}" errorParsers="" id="com.telink.tc32eclipse.tool.size.TC32Win.app.debug.2075153496" name="Print Size" superClass="com.telink.tc32eclipse.tool.size.TC32Win.app.debug"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="boot/B87|vendor/b85m_driver_test|vendor/b85m_module|vendor/b85m_master_kma_dongle|vendor/b85m_internal_test|vendor/b85m_hci|vendor/b85m_feature_test|vendor/b85m_ble_remote|vendor/827x_hci|vendor/827x_internal_test|vendor/827x_master_kma_dongle|vendor/827x_module|vendor/827x_ble_remote|vendor/827x_ble_sample|vendor/827x_feature_test|vendor/82x8_module|vendor/82x8_master_kma_dongle|vendor/82x8_hci|vendor/82x8_ble_remote|vendor/8258_internal_test|vendor/8258_module|vendor/8258_bqb_lowertester|vendor/8258_hci|vendor/8255_ble_remote|vendor/8258_bqb_uppertester|vendor/8258_feature_test|drivers/8278|vendor/8258_driver_test|vendor/8258_master_slave|vendor/8255_driver_test|vendor/82x8_internal_test|vendor/8258_ble_remote|drivers/8255|vendor/8258_master_kma_dongle|vendor/82x8_feature_test" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="org.eclipse.cdt.core.language.mapping"/>
			<storageModule moduleId="org.eclipse.cdt.internal.ui.text.commentOwnerProjectMappings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="ble_lt_ms.com.telink.tc32eclipse.project.TC32Win.elf_0.1.0.33823561" name="TC32 Cross Target Application" projectType="com.telink.tc32eclipse.project.TC32Win.elf_0.1.0"/>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="825x_ble_sample">
			<resource resourceType="PROJECT" workspacePath="/FN_Project2"/>
		</configuration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
			<buildOutputProvider>
				<openAction enabled="true" filePath=""/>
				<parser enabled="true"/>
			</buildOutputProvider>
			<scannerInfoProvider id="specsFile">
				<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
				<parser enabled="true"/>
			</scannerInfoProvider>
		</profile>
		<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
			<buildOutputProvider>
				<openAction enabled="true" filePath=""/>
				<parser enabled="true"/>
			</buildOutputProvider>
			<scannerInfoProvider id="specsFile">
				<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
				<parser enabled="true"/>
			</scannerInfoProvider>
		</profile>
		<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
			<buildOutputProvider>
				<openAction enabled="true" filePath=""/>
				<parser enabled="true"/>
			</buildOutputProvider>
			<scannerInfoProvider id="specsFile">
				<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
				<parser enabled="true"/>
			</scannerInfoProvider>
		</profile>
		<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
			<buildOutputProvider>
				<openAction enabled="true" filePath=""/>
				<parser enabled="true"/>
			</buildOutputProvider>
			<scannerInfoProvider id="specsFile">
				<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
				<parser enabled="true"/>
			</scannerInfoProvider>
		</profile>
		<scannerConfigBuildInfo instanceId="com.telink.tc32eclipse.configuration.app.release.**********;com.telink.tc32eclipse.configuration.app.release.**********.;com.telink.tc32eclipse.tool.compiler.TC32Win.app.release.456037511;com.telink.tc32eclipse.compiler.TC32Win.input.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.telink.tc32eclipse.core.TC32GCCManagedMakePerProjectProfileC"/>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.telink.tc32eclipse.configuration.app.debug.**********;com.telink.tc32eclipse.configuration.app.debug.**********.">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.telink.tc32eclipse.core.TC32GCCManagedMakePerProjectProfileC"/>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.telink.tc32eclipse.configuration.app.debug.**********.351063629.799040038.**********;com.telink.tc32eclipse.configuration.app.debug.**********.351063629.799040038.**********.">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.telink.tc32eclipse.core.TC32GCCManagedMakePerProjectProfileC"/>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.telink.tc32eclipse.configuration.app.debug.**********.351063629;com.telink.tc32eclipse.configuration.app.debug.**********.351063629.">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.telink.tc32eclipse.core.TC32GCCManagedMakePerProjectProfileC"/>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.telink.tc32eclipse.configuration.app.release.**********;com.telink.tc32eclipse.configuration.app.release.**********.">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.telink.tc32eclipse.core.TC32GCCManagedMakePerProjectProfileC"/>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.telink.tc32eclipse.configuration.app.debug.**********.351063629.799040038.**********;com.telink.tc32eclipse.configuration.app.debug.**********.351063629.799040038.**********.;com.telink.tc32eclipse.tool.compiler.TC32Win.app.debug.**********;com.telink.tc32eclipse.compiler.TC32Win.input.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.telink.tc32eclipse.core.TC32GCCManagedMakePerProjectProfileC"/>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.telink.tc32eclipse.configuration.app.debug.**********.351063629;com.telink.tc32eclipse.configuration.app.debug.**********.351063629.;com.telink.tc32eclipse.tool.compiler.TC32Win.app.debug.928928380;com.telink.tc32eclipse.compiler.TC32Win.input.681259007">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.telink.tc32eclipse.core.TC32GCCManagedMakePerProjectProfileC"/>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.telink.tc32eclipse.configuration.app.debug.**********;com.telink.tc32eclipse.configuration.app.debug.**********.;com.telink.tc32eclipse.tool.compiler.TC32Win.app.debug.**********;com.telink.tc32eclipse.compiler.TC32Win.input.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.telink.tc32eclipse.core.TC32GCCManagedMakePerProjectProfileC"/>
			<profile id="org.eclipse.cdt.make.core.GCCStandardMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfile">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/${specs_file}" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.cpp" command="g++" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
			<profile id="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC">
				<buildOutputProvider>
					<openAction enabled="true" filePath=""/>
					<parser enabled="true"/>
				</buildOutputProvider>
				<scannerInfoProvider id="specsFile">
					<runAction arguments="-E -P -v -dD ${plugin_state_location}/specs.c" command="gcc" useDefault="true"/>
					<parser enabled="true"/>
				</scannerInfoProvider>
			</profile>
		</scannerConfigBuildInfo>
	</storageModule>
</cproject>