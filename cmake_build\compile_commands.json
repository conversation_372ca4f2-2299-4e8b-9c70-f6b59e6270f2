[{"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -g -DMCU_STARTUP_8258 -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\div_mod.S.obj -c D:\\Telink_Project\\FN_Project\\div_mod.S", "file": "D:/Telink_Project/FN_Project/div_mod.S", "output": "CMakeFiles/825x_ble_sample.dir/div_mod.S.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\application\\app\\usbaud.c.obj -c D:\\Telink_Project\\FN_Project\\application\\app\\usbaud.c", "file": "D:/Telink_Project/FN_Project/application/app/usbaud.c", "output": "CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\application\\app\\usbcdc.c.obj -c D:\\Telink_Project\\FN_Project\\application\\app\\usbcdc.c", "file": "D:/Telink_Project/FN_Project/application/app/usbcdc.c", "output": "CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\application\\app\\usbkb.c.obj -c D:\\Telink_Project\\FN_Project\\application\\app\\usbkb.c", "file": "D:/Telink_Project/FN_Project/application/app/usbkb.c", "output": "CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\application\\app\\usbmouse.c.obj -c D:\\Telink_Project\\FN_Project\\application\\app\\usbmouse.c", "file": "D:/Telink_Project/FN_Project/application/app/usbmouse.c", "output": "CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\application\\audio\\adpcm.c.obj -c D:\\Telink_Project\\FN_Project\\application\\audio\\adpcm.c", "file": "D:/Telink_Project/FN_Project/application/audio/adpcm.c", "output": "CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\application\\audio\\gl_audio.c.obj -c D:\\Telink_Project\\FN_Project\\application\\audio\\gl_audio.c", "file": "D:/Telink_Project/FN_Project/application/audio/gl_audio.c", "output": "CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\application\\audio\\tl_audio.c.obj -c D:\\Telink_Project\\FN_Project\\application\\audio\\tl_audio.c", "file": "D:/Telink_Project/FN_Project/application/audio/tl_audio.c", "output": "CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\application\\keyboard\\keyboard.c.obj -c D:\\Telink_Project\\FN_Project\\application\\keyboard\\keyboard.c", "file": "D:/Telink_Project/FN_Project/application/keyboard/keyboard.c", "output": "CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\application\\print\\putchar.c.obj -c D:\\Telink_Project\\FN_Project\\application\\print\\putchar.c", "file": "D:/Telink_Project/FN_Project/application/print/putchar.c", "output": "CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\application\\print\\u_printf.c.obj -c D:\\Telink_Project\\FN_Project\\application\\print\\u_printf.c", "file": "D:/Telink_Project/FN_Project/application/print/u_printf.c", "output": "CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\application\\usbstd\\usb.c.obj -c D:\\Telink_Project\\FN_Project\\application\\usbstd\\usb.c", "file": "D:/Telink_Project/FN_Project/application/usbstd/usb.c", "output": "CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\application\\usbstd\\usbdesc.c.obj -c D:\\Telink_Project\\FN_Project\\application\\usbstd\\usbdesc.c", "file": "D:/Telink_Project/FN_Project/application/usbstd/usbdesc.c", "output": "CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -g -DMCU_STARTUP_8258 -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\boot\\B85\\cstartup_825x.S.obj -c D:\\Telink_Project\\FN_Project\\boot\\B85\\cstartup_825x.S", "file": "D:/Telink_Project/FN_Project/boot/B85/cstartup_825x.S", "output": "CMakeFiles/825x_ble_sample.dir/boot/B85/cstartup_825x.S.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\common\\sdk_version.c.obj -c D:\\Telink_Project\\FN_Project\\common\\sdk_version.c", "file": "D:/Telink_Project/FN_Project/common/sdk_version.c", "output": "CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\common\\string.c.obj -c D:\\Telink_Project\\FN_Project\\common\\string.c", "file": "D:/Telink_Project/FN_Project/common/string.c", "output": "CMakeFiles/825x_ble_sample.dir/common/string.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\common\\utility.c.obj -c D:\\Telink_Project\\FN_Project\\common\\utility.c", "file": "D:/Telink_Project/FN_Project/common/utility.c", "output": "CMakeFiles/825x_ble_sample.dir/common/utility.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\adc.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\adc.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/adc.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\aes.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\aes.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/aes.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\analog.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\analog.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/analog.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\audio.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\audio.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/audio.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\bsp.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\bsp.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/bsp.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\clock.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\clock.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/clock.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\driver_ext\\ext_calibration.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\driver_ext\\ext_calibration.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/driver_ext/ext_calibration.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\driver_ext\\ext_misc.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\driver_ext\\ext_misc.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/driver_ext/ext_misc.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\driver_ext\\rf_pa.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\driver_ext\\rf_pa.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/driver_ext/rf_pa.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\driver_ext\\software_uart.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\driver_ext\\software_uart.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/driver_ext/software_uart.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\emi.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\emi.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/emi.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\flash.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\flash.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/flash.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\flash\\flash_mid011460c8.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\flash\\flash_mid011460c8.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid011460c8.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\flash\\flash_mid1060c8.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\flash\\flash_mid1060c8.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid1060c8.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\flash\\flash_mid13325e.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\flash\\flash_mid13325e.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid13325e.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\flash\\flash_mid134051.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\flash\\flash_mid134051.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid134051.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\flash\\flash_mid136085.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\flash\\flash_mid136085.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid136085.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\flash\\flash_mid1360c8.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\flash\\flash_mid1360c8.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid1360c8.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\flash\\flash_mid1360eb.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\flash\\flash_mid1360eb.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid1360eb.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\flash\\flash_mid14325e.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\flash\\flash_mid14325e.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid14325e.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\flash\\flash_mid1460c8.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\flash\\flash_mid1460c8.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/flash/flash_mid1460c8.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\gpio.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\gpio.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/gpio.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\i2c.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\i2c.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/i2c.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\lpc.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\lpc.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/lpc.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\qdec.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\qdec.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/qdec.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\s7816.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\s7816.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/s7816.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\spi.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\spi.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/spi.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\timer.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\timer.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/timer.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\uart.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\uart.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/uart.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\usbhw.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\usbhw.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/usbhw.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\drivers\\8258\\watchdog.c.obj -c D:\\Telink_Project\\FN_Project\\drivers\\8258\\watchdog.c", "file": "D:/Telink_Project/FN_Project/drivers/8258/watchdog.c", "output": "CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\b85m_ble_sample\\app.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\b85m_ble_sample\\app.c", "file": "D:/Telink_Project/FN_Project/vendor/b85m_ble_sample/app.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\b85m_ble_sample\\app_att.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\b85m_ble_sample\\app_att.c", "file": "D:/Telink_Project/FN_Project/vendor/b85m_ble_sample/app_att.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\b85m_ble_sample\\app_ui.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\b85m_ble_sample\\app_ui.c", "file": "D:/Telink_Project/FN_Project/vendor/b85m_ble_sample/app_ui.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\b85m_ble_sample\\main.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\b85m_ble_sample\\main.c", "file": "D:/Telink_Project/FN_Project/vendor/b85m_ble_sample/main.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\common\\app_buffer.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\common\\app_buffer.c", "file": "D:/Telink_Project/FN_Project/vendor/common/app_buffer.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\common\\app_common.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\common\\app_common.c", "file": "D:/Telink_Project/FN_Project/vendor/common/app_common.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\common\\battery_check.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\common\\battery_check.c", "file": "D:/Telink_Project/FN_Project/vendor/common/battery_check.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\common\\ble_flash.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\common\\ble_flash.c", "file": "D:/Telink_Project/FN_Project/vendor/common/ble_flash.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\common\\blt_fw_sign.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\common\\blt_fw_sign.c", "file": "D:/Telink_Project/FN_Project/vendor/common/blt_fw_sign.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\common\\blt_led.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\common\\blt_led.c", "file": "D:/Telink_Project/FN_Project/vendor/common/blt_led.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\common\\blt_soft_timer.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\common\\blt_soft_timer.c", "file": "D:/Telink_Project/FN_Project/vendor/common/blt_soft_timer.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\common\\custom_pair.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\common\\custom_pair.c", "file": "D:/Telink_Project/FN_Project/vendor/common/custom_pair.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\common\\flash_fw_check.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\common\\flash_fw_check.c", "file": "D:/Telink_Project/FN_Project/vendor/common/flash_fw_check.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\common\\flash_prot.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\common\\flash_prot.c", "file": "D:/Telink_Project/FN_Project/vendor/common/flash_prot.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\common\\simple_sdp.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\common\\simple_sdp.c", "file": "D:/Telink_Project/FN_Project/vendor/common/simple_sdp.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\common\\tlkapi_debug.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\common\\tlkapi_debug.c", "file": "D:/Telink_Project/FN_Project/vendor/common/tlkapi_debug.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\common\\user_config.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\common\\user_config.c", "file": "D:/Telink_Project/FN_Project/vendor/common/user_config.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\user_app\\agreement\\fn\\fn_agreement.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\user_app\\agreement\\fn\\fn_agreement.c", "file": "D:/Telink_Project/FN_Project/vendor/user_app/agreement/fn/fn_agreement.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/fn/fn_agreement.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\user_app\\at_cmd\\app_at_cmd.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\user_app\\at_cmd\\app_at_cmd.c", "file": "D:/Telink_Project/FN_Project/vendor/user_app/at_cmd/app_at_cmd.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\user_app\\att_ble\\app_ble.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\user_app\\att_ble\\app_ble.c", "file": "D:/Telink_Project/FN_Project/vendor/user_app/att_ble/app_ble.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\user_app\\bms\\bms_data.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\user_app\\bms\\bms_data.c", "file": "D:/Telink_Project/FN_Project/vendor/user_app/bms/bms_data.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\user_app\\bms\\zy\\sh367601xb.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\user_app\\bms\\zy\\sh367601xb.c", "file": "D:/Telink_Project/FN_Project/vendor/user_app/bms/zy/sh367601xb.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\user_app\\bms\\zy\\sh367601xb_conversion.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\user_app\\bms\\zy\\sh367601xb_conversion.c", "file": "D:/Telink_Project/FN_Project/vendor/user_app/bms/zy/sh367601xb_conversion.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_conversion.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\user_app\\flash\\user_app_flash.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\user_app\\flash\\user_app_flash.c", "file": "D:/Telink_Project/FN_Project/vendor/user_app/flash/user_app_flash.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\user_app\\list\\queue\\queue.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\user_app\\list\\queue\\queue.c", "file": "D:/Telink_Project/FN_Project/vendor/user_app/list/queue/queue.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\user_app\\system_main\\app_mian.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\user_app\\system_main\\app_mian.c", "file": "D:/Telink_Project/FN_Project/vendor/user_app/system_main/app_mian.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\user_app\\uart\\app_usart.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\user_app\\uart\\app_usart.c", "file": "D:/Telink_Project/FN_Project/vendor/user_app/uart/app_usart.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\user_app\\user_app_main.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\user_app\\user_app_main.c", "file": "D:/Telink_Project/FN_Project/vendor/user_app/user_app_main.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.obj"}, {"directory": "D:/Telink_Project/FN_Project/cmake_build", "command": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin\\tc32-elf-gcc   -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/FN_Project/././ -ID:/Telink_Project/FN_Project/./vendor/common -ID:/Telink_Project/FN_Project/./common -ID:/Telink_Project/FN_Project/./drivers/8258 -ID:/Telink_Project/FN_Project/./vendor/user_app/bms -ID:/Telink_Project/FN_Project/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall -o CMakeFiles\\825x_ble_sample.dir\\vendor\\user_app\\user_app_rs2058.c.obj -c D:\\Telink_Project\\FN_Project\\vendor\\user_app\\user_app_rs2058.c", "file": "D:/Telink_Project/FN_Project/vendor/user_app/user_app_rs2058.c", "output": "CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_rs2058.c.obj"}]