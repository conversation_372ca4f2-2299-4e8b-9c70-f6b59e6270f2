{"inputs": [{"path": "CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeSystem.cmake.in"}, {"isGenerated": true, "path": "cmake_build/CMakeFiles/4.1.0-rc1/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeMinGWFindMake.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-Initialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeASMCompiler.cmake.in"}, {"isGenerated": true, "path": "cmake_build/CMakeFiles/4.1.0-rc1/CMakeASMCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/CrayClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/Diab-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/HP-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/LCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/OrangeC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/Renesas-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/TIClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/Tasking-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/XL-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/zOS-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "cmake_build/CMakeFiles/4.1.0-rc1/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeASMInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-ASM.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU-ASM.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeRCCompiler.cmake.in"}, {"isGenerated": true, "path": "cmake_build/CMakeFiles/4.1.0-rc1/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-windres.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestASMCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeASMLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/Windows-ASM.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU-ASM.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeInspectASMLinker.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeASMCompiler.cmake.in"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/Windows-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeInspectCLinker.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCCompiler.cmake.in"}], "kind": "cmakeFiles", "paths": {"build": "D:/Telink_Project/FN_Project/cmake_build", "source": "D:/Telink_Project/FN_Project"}, "version": {"major": 1, "minor": 1}}