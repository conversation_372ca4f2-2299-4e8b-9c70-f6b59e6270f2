#ifndef _USER_APP_MAIN_H_
#define _USER_APP_MAIN_H_

#include "drivers.h"
#include "tl_common.h"
#include "stack/ble/ble.h"
#include "uart/app_usart.h"
#include "user_app_rs2058.h"
#include "../b85m_ble_sample/app.h"
#include "../b85m_ble_sample/app_att.h"
#include "../common/tlkapi_debug.h"
#include "list/list_type.h"



/* 16位大端转小端 */
#define BE16_TO_LE16(x) ((uint16_t)( \
    (((uint16_t)(x) & 0xFF00) >> 8) | \
    (((uint16_t)(x) & 0x00FF) << 8)))
/* 32位大端转小端 */ 
#define BE32_TO_LE32(x) ((uint32_t)( \
    (((uint32_t)(x) & 0xFF000000) >> 24) | \
    (((uint32_t)(x) & 0x00FF0000) >> 8)  | \
    (((uint32_t)(x) & 0x0000FF00) << 8)  | \
    (((uint32_t)(x) & 0x000000FF) << 24)))



/* 电池状态 */
typedef enum 
{
    BATT_IDLE_STATE,            /* 空闲 */
    BATT_DISCHARGE_STATE,       /* 放电 */
    BATT_CHARGING_STATE,        /* 充电 */
}BATT_STATE;
/* 任务序号 */
typedef enum
{
    QUEUE_CURR_TASK,
    QUEUE_RAM1_TASK,
    QUEUE_RAM2_TASK,
    QUEUE_ROM1_TASK,
    QUEUE_ROM2_TASK,
    QUEUE_WRITE1_ENABLE_TASK,
    QUEUE_WRITE2_ENABLE_TASK,
    QUEUE_WRITE1_01_TASK,
    QUEUE_WRITE1_02_TASK,
    QUEUE_WRITE1_03_TASK,
    QUEUE_WRITE1_04_TASK,
    QUEUE_WRITE1_05_TASK,
    QUEUE_WRITE1_06_TASK,
    QUEUE_WRITE1_07_TASK,
    QUEUE_WRITE1_08_TASK,
    QUEUE_WRITE1_09_TASK,
    QUEUE_WRITE1_0A_TASK,
    QUEUE_WRITE1_0B_TASK,
    QUEUE_WRITE1_0C_TASK,
    QUEUE_WRITE1_0D_TASK,
    QUEUE_WRITE1_0E_TASK,
    QUEUE_WRITE1_0F_TASK,
    QUEUE_WRITE1_10_TASK,
    QUEUE_WRITE1_11_TASK,
    QUEUE_WRITE1_12_TASK,
    QUEUE_WRITE1_13_TASK,
    QUEUE_WRITE1_14_TASK,
    QUEUE_WRITE1_15_TASK,
    QUEUE_WRITE2_01_TASK,
    QUEUE_WRITE2_02_TASK,
    QUEUE_WRITE2_03_TASK,
    QUEUE_WRITE2_04_TASK,
    QUEUE_WRITE2_05_TASK,
    QUEUE_WRITE2_06_TASK,
    QUEUE_WRITE2_07_TASK,
    QUEUE_WRITE2_08_TASK,
    QUEUE_WRITE2_09_TASK,
    QUEUE_WRITE2_0A_TASK,
    QUEUE_WRITE2_0B_TASK,
    QUEUE_WRITE2_0C_TASK,
    QUEUE_WRITE2_0D_TASK,
    QUEUE_WRITE2_0E_TASK,
    QUEUE_WRITE2_0F_TASK,
    QUEUE_WRITE2_10_TASK,
    QUEUE_WRITE2_11_TASK,
    QUEUE_WRITE2_12_TASK,
    QUEUE_WRITE2_13_TASK,
    QUEUE_WRITE2_14_TASK,
    QUEUE_WRITE2_15_TASK,
    QUEUE_RESET1_TASK,
    QUEUE_RESET2_TASK,
}TIME_QUEUE_TASK_FLG;
/* 每个任务执行完后的延迟时间 */
typedef enum
{
    QUEUE_CURR_TIME = 20,
    QUEUE_RAM1_TIME = 80,
    QUEUE_RAM2_TIME = 80,
    QUEUE_ROM1_TIME = 40,
    QUEUE_ROM2_TIME = 40,
    QUEUE_WRITE1_ENABLE_TIME = 20,
    QUEUE_WRITE2_ENABLE_TIME = 20,
    QUEUE_WRITE1_TIME = 20,
    QUEUE_WRITE2_TIME = 20,
    QUEUE_RESET1_TIME = 100,
    QUEUE_RESET2_TIME = 100,
}TIME_QUEUE_TASK_TIME;



/* 串口结构体 */
typedef struct
{
    unsigned char len;                          /* 长度 */
    unsigned char flg;                          /* 标志 */
    unsigned char buff[255];                    /* 数据 */
}User_App_Uart_TypeDef;
/* 蓝牙结构体 */
typedef struct
{
    unsigned char connect;                      /* 连接状态 */     
    unsigned char len;                          /* 长度 */
    unsigned char flg;                          /* 标志 */
    unsigned char buff[255];                    /* 数据 */
}User_App_Ble_TypeDef;




extern void User_App_Init(void);
extern void time0_callback(void);
extern void time1_callback(void);
extern void User_App_Logic(void);

extern User_App_Uart_TypeDef Uart;
extern User_App_Ble_TypeDef  Ble;

#endif