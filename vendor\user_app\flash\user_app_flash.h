#ifndef _USER_APP_FLASH_H_
#define _USER_APP_FLASH_H_
/* 自定义标志位数据 */
#define USER_APP_FLASH_FLG 0xDA
/* 零点电流 */
#define FLASH_ADDR_ZERO_CURRENT_FLG 0x40000
#define FLASH_ADDR_ZERO_CURRENT     0x40001
#define FLASH_ADDR_ZERO_CURRENT_LEN 0x05
/* 采样电阻 */
#define FLASH_ADDR_RESISTANCE_FLG 0x4000A
#define FLASH_ADDR_RESISTANCE     0x4000B
#define FLASH_ADDR_RESISTANCE_LEN 0x05
/* 单体补偿电压 */
#define FLASH_ADDR_VOLTAGE_FLG 0x40014
#define FLASH_ADDR_VOLTAGE     0x40015
#define FLASH_ADDR_VOLTAGE_LEN 0x35
/* 电池总容量 */
#define FLASH_ADDR_BATT_CAPACITY_FLG  0x40049
#define FLASH_ADDR_BATT_CAPACITY     0x40050
#define FLASH_ADDR_BATT_CAPACITY_LEN 0x05
/* 产品序列号 */
#define FLASH_ADDR_SERIAL_NUMBER_FLG 0x40060
#define FLASH_ADDR_SERIAL_NUMBER     0x40061
#define FLASH_ADDR_SERIAL_NUMBER_LEN 0x1F
/* 出厂信息 */
#define FLASH_ADDR_EX_FACTORY_FLG 0x40093
#define FLASH_ADDR_EX_FACTORY     0x40094
#define FLASH_ADDR_EX_FACTORY_LEN 0x09
/* 累计放电、soc、写次数 */
#define FLASH_ADDR_ACC_DISCHARGE_FLG 0x41000
#define FLASH_ADDR_ACC_DISCHARGE     0x41001
#define FLASH_ADDR_ACC_DISCHARGE_LEN 0x0A



extern int My_Flash_Write(unsigned int addr, unsigned char *data, unsigned int len);


#endif