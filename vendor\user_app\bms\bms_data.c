#include "bms_data.h"
#include "tl_common.h"
#include "zy/sh367601xb_conversion.h"

/* ==========================================通用管理器初始化宏========================================== */

/**
 * @brief 定义基础管理器初始化函数的宏（无专业数据处理方法）
 * @param ManagerType 管理器类型名
 * @param DataType 数据类型名
 */
#define DEFINE_BASIC_MANAGER_INIT(ManagerType, DataType) \
static void manager_init_##ManagerType(ManagerType* self) { \
    if (self == NULL) return; \
    memset(&self->data, 0, sizeof(DataType)); \
    printf(#ManagerType ": Initialized\n"); \
} \
int manager_init_func_##ManagerType(ManagerType* self) { \
    if (self == NULL) return -1; \
    self->methods.init = manager_init_##ManagerType; \
    return 0; \
}

/**
 * @brief 定义带专业数据处理方法的管理器初始化宏
 * @param ManagerType 管理器类型名
 * @param DataType 数据类型名
 * @param ProcessMethod 专业数据处理方法名
 */
#define DEFINE_MANAGER_WITH_PROCESSOR(ManagerType, DataType, ProcessMethod) \
static void manager_init_##ManagerType(ManagerType* self) { \
    if (self == NULL) return; \
    memset(&self->data, 0, sizeof(DataType)); \
    self->methods.ProcessMethod = ManagerType##_##ProcessMethod; \
    printf(#ManagerType ": Initialized with " #ProcessMethod " method\n"); \
} \
int manager_init_func_##ManagerType(ManagerType* self) { \
    if (self == NULL) return -1; \
    self->methods.init = manager_init_##ManagerType; \
    /* 确保专业数据处理方法指针在这里也设置 */ \
    self->methods.ProcessMethod = ManagerType##_##ProcessMethod; \
    return 0; \
}

/* ==========================================各管理器数据处理方法实现========================================== */
/* 电压管理器专业数据处理方法 */
static void VoltageManager_process_voltage_data(VoltageManager* self, unsigned short* cells, int battery_count)
{
    if (self == NULL || cells == NULL) return;

    unsigned long total = 0;
    unsigned short max_voltage = 0, min_voltage = 65535;

    self->data.battery_count = battery_count;

    for (int i = 0; i < battery_count; i++) 
    {
        self->data.cell_voltages[i] = cells[i];
        if (self->data.cell_voltages[i] == 0 || self->data.cell_voltages[i] > 5000) 
            self->data.cell_voltages[i] = 0;

        /* 计算统计值 */
        total += self->data.cell_voltages[i];
        if (self->data.cell_voltages[i] > max_voltage) 
            max_voltage = self->data.cell_voltages[i];
        if (self->data.cell_voltages[i] < min_voltage) 
            min_voltage = self->data.cell_voltages[i];
    }

    self->data.total_voltage = (unsigned int)total;
    self->data.max_voltage = max_voltage;
    self->data.min_voltage = min_voltage;
    self->data.voltage_diff = max_voltage - min_voltage;

    printf("VoltageManager: Processed (Cells: %d, Total: %d mV, Diff: %d mV)\n",
           self->data.battery_count, self->data.total_voltage, self->data.voltage_diff);
}

/* 电流管理器专业数据处理方法 */
static void CurrentManager_process_current_data(CurrentManager* self, unsigned short current, bool charge_status, bool discharge_status)
{
    if (self == NULL) return;

    /* 更新电流管理器数据 */
    self->data.total_current = current;

    /* 更新电流状态 */
    if (discharge_status)
        self->data.current_state = CURRENT_STATE_DISCHARGING;
    else if (charge_status)
        self->data.current_state = CURRENT_STATE_CHARGING;
    else
        self->data.current_state = CURRENT_STATE_IDLE;

    printf("CurrentManager: Processed (Current: %d mA, State: %d)\n",
           current, self->data.current_state);
}

/* 温度管理器专业数据处理方法 */
static void TemperatureManager_process_temperature_data(TemperatureManager* self, signed char* external_temps, unsigned char temp_count, signed char chip_temp)
{
    if (self == NULL || external_temps == NULL) return;

    /* 更新温度传感器数量 */
    self->data.external_temp_count = temp_count > 10 ? 10 : temp_count;
    self->data.chip_temp_count = 1;
    self->data.mos_temp_count = 0; // 暂时未使用

    /* 更新芯片温度 */
    self->data.chip_temp = chip_temp;

    /* 更新外部温度数组 */
    signed char max_temp = external_temps[0], min_temp = external_temps[0];
    for (int i = 0; i < self->data.external_temp_count; i++) {
        self->data.external_temp[i] = external_temps[i];
        if (external_temps[i] > max_temp) max_temp = external_temps[i];
        if (external_temps[i] < min_temp) min_temp = external_temps[i];
    }
    
    /* 计算温度统计值 */
    self->data.max_external_temp = max_temp;
    self->data.min_external_temp = min_temp;

    printf("TemperatureManager: Processed (%d sensors, Range: %d-%d°C, Chip: %d°C)\n",
           self->data.external_temp_count, min_temp, max_temp, chip_temp);
}

/* 电池状态管理器专业数据处理方法 */
static void BatteryStateManager_process_battery_state_data(BatteryStateManager* self, unsigned int cycle_count, float current_ma, float battery_capacity)
{
    if (self == NULL) return;

    /* 1. 更新采样时间 */
    self->data.sampling_time_curr = clock_time();
    unsigned long time_ms = 0;
    if (self->data.sampling_time_last > 0 && self->data.sampling_time_curr > self->data.sampling_time_last) {
        time_ms = self->data.sampling_time_curr - self->data.sampling_time_last;
    }
    self->data.sampling_time_last = self->data.sampling_time_curr;

    /* 2. 计算SOC */
    if (time_ms > 0 && battery_capacity > 0) {
        /* 判断充放电方向：正值为放电，负值为充电 */
        unsigned char direction = (current_ma > 0) ? 1 : 0;  // 1=放电，0=充电
        
        /* 调用SOC更新函数 */
        float current_soc = (float)self->data.soc / 100.0f;  // 转换为百分比
        current_soc = update_soc_simple(current_soc, battery_capacity, current_ma, (float)time_ms, direction);
        self->data.soc = (unsigned short)(current_soc * 100.0f);  // 转换回存储格式
        
        /* SOC范围限制 */
        if (self->data.soc > 10000) self->data.soc = 10000;  // 100.00%
        if (self->data.soc < 0) self->data.soc = 0;          // 0.00%
    }

    /* 3. 计算SOH（电池健康度） - 基于循环次数 */
    if (cycle_count > 0) 
    {
        /* 每1000个循环容量衰减30%，最大衰减30% */
        float degradation_rate = (float)cycle_count / 1000.0f * 0.3f;
        if (degradation_rate > 0.3f) degradation_rate = 0.3f;
        self->data.soh = (unsigned short)((1.0f - degradation_rate) * 10000.0f);  // 0.01%精度
    } 
    else 
        self->data.soh = 10000; // 新电池健康度为100.00%
    
    /* SOH范围限制 */
    if (self->data.soh > 10000) self->data.soh = 10000;  // 100.00%
    if (self->data.soh < 7000) self->data.soh = 7000;    // 70.00%

    printf("BatteryStateManager: Processed (SOC: %.2f%%, SOH: %.2f%%)\n",
           (float)self->data.soc / 100.0f, (float)self->data.soh / 100.0f);
}

/* 充放电管理器专业数据处理方法 */
static void ChargeDischargeManager_process_charge_discharge_data(ChargeDischargeManager* self, int current_ma, bool charge_status, bool discharge_status, unsigned int total_voltage_mv, unsigned int battery_capacity)
{
    if (self == NULL) return;
    
    float filtered_current = (float)current_ma;

    /* 1. 计算时间间隔 */
    static unsigned long last_update_time = 0;
    unsigned long current_time = clock_time();
    float time_delta_seconds = 0.0f;
    
    if (last_update_time > 0 && current_time > last_update_time) 
        time_delta_seconds = (float)((current_time - last_update_time) / 16000); // ms转秒
    last_update_time = current_time;
    
    /* 2. 累计容量计算 */
    if (time_delta_seconds > 0) 
    {
        float capacity_delta_mah = (filtered_current * time_delta_seconds) / 3600.0f; // mAh
        
        if (discharge_status && capacity_delta_mah > 0) {
            /* 放电状态：累计放电量 */
            self->data.accumulated_discharge += (unsigned int)capacity_delta_mah;
            
            /* 更新剩余容量 */
            if (self->data.remaining_capacity > capacity_delta_mah) 
                self->data.remaining_capacity -= (unsigned int)capacity_delta_mah;
            else 
                self->data.remaining_capacity = 0;
        } 
        else if (charge_status && capacity_delta_mah < 0) 
        {
            /* 充电状态：累计充电量 */
            self->data.accumulated_charge += (unsigned int)(-capacity_delta_mah);
            /* 更新剩余容量 */
            self->data.remaining_capacity += (unsigned int)(-capacity_delta_mah);
        }
    }

    /* 3. 更新功率 */
    float power_w = filtered_current * total_voltage_mv / 1000.0f;
    self->data.power = (unsigned int)(power_w * 1000.0f); // 转换为mW

    /* 4. 更新循环计数 - 基于累计放电量计算 */
    if (self->data.accumulated_discharge > 0 && battery_capacity > 0) {
        unsigned int new_cycle_count = self->data.accumulated_discharge / battery_capacity;
        
        /* 检测循环数增加 */
        if (new_cycle_count > self->data.cycle_count)
            self->data.cycle_count = new_cycle_count;
    }

    printf("ChargeDischargeManager: Processed (Power: %.1f mW, Cycle: %d)\n",
           power_w, self->data.cycle_count);
}

/* 告警管理器专业数据处理方法 */
static void AlarmManager_process_alarm_data(AlarmManager* self, AlarmData alarm_data)
{
    if (self == NULL) return;

    /* 直接复制告警数据结构 */
    self->data = alarm_data;

    printf("AlarmManager: Processed alarm data\n");
}

/* 状态管理器专业数据处理方法 */
static void StatusManager_process_status_data(StatusManager* self, StatusData status_data)
{
    if (self == NULL) return;

    /* 直接复制状态数据结构 */
    self->data = status_data;

    printf("StatusManager: Processed status data\n");
}

/* 保护参数管理器专业数据处理方法 */
static void ProtectionParameterManager_process_protection_data(ProtectionParameterManager* self, 
                                      const ProtectionParameters* params)
{
    if (self == NULL || params == NULL) return;

    /* 直接复制整个结构体 */
    self->data = *params;

    printf("ProtectionParameterManager: Processed (OV: %dmV, UV: %dmV, OCD1: %dmV, OCC: %dmV)\n",
           params->overvoltage_protection, params->undervoltage_protection, 
           params->discharge_overcurrent1, params->charge_overcurrent1);
}

/* ==========================================使用宏定义所有管理器初始化========================================== */

/* 使用宏初始化带专业数据处理方法的管理器 */
DEFINE_MANAGER_WITH_PROCESSOR(AlarmManager, AlarmData, process_alarm_data)
DEFINE_MANAGER_WITH_PROCESSOR(StatusManager, StatusData, process_status_data)
DEFINE_MANAGER_WITH_PROCESSOR(VoltageManager, VoltageData, process_voltage_data)
DEFINE_MANAGER_WITH_PROCESSOR(CurrentManager, CurrentData, process_current_data)
DEFINE_MANAGER_WITH_PROCESSOR(ChargeDischargeManager, ChargeDischargeData, process_charge_discharge_data)
DEFINE_MANAGER_WITH_PROCESSOR(TemperatureManager, TemperatureData, process_temperature_data)
DEFINE_MANAGER_WITH_PROCESSOR(ProtectionParameterManager, ProtectionParameters, process_protection_data)

/* 使用基础宏初始化无专业数据处理方法的管理器 */
DEFINE_BASIC_MANAGER_INIT(CustomParameterManager, CustomParameters)

/* 定义管理器初始化和启用的宏 */
#define INIT_AND_ENABLE_MANAGER(self, mgr_name, mgr_type, display_name) \
    if (manager_init_func_##mgr_type(&self->mgr_name) == 0) { \
        self->mgr_name.methods.init(&self->mgr_name); \
        printf(display_name " manager init success\n"); \
    } else { \
        printf(display_name " manager init failed\n"); \
    }

/* 专门用于带专业数据处理方法的管理器初始化宏 */
#define INIT_AND_ENABLE_PROCESSOR_MANAGER(self, mgr_name, mgr_type, display_name, process_method) \
    if (manager_init_func_##mgr_type(&self->mgr_name) == 0) { \
        self->mgr_name.methods.init(&self->mgr_name); \
        /* 双重确保专业数据处理方法指针设置正确 */ \
        if (self->mgr_name.methods.process_method == NULL) { \
            self->mgr_name.methods.process_method = mgr_type##_##process_method; \
            printf(display_name " manager: method pointer manually assigned\n"); \
        } \
        printf(display_name " manager init success with processor\n"); \
    } else { \
        printf(display_name " manager init failed\n"); \
    }

/* ==========================================BMS总数据管理器实现========================================== */
static void bms_data_mgr_init_method(BMS_DataManager* self)
{
    if (self == NULL) return;

    /* 使用新宏初始化带专业数据处理方法的管理器 */
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, alarm_mgr, AlarmManager, "Alarm", process_alarm_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, status_mgr, StatusManager, "Status", process_status_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, voltage_mgr, VoltageManager, "Voltage", process_voltage_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, current_mgr, CurrentManager, "Current", process_current_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, temp_mgr, TemperatureManager, "Temperature", process_temperature_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, charge_discharge_mgr, ChargeDischargeManager, "Charge/Discharge", process_charge_discharge_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, battery_state_mgr, BatteryStateManager, "Battery state", process_battery_state_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, protection_param_mgr, ProtectionParameterManager, "Protection param", process_protection_data);
    
    /* 使用基础宏初始化无专业数据处理方法的管理器 */
    INIT_AND_ENABLE_MANAGER(self, custom_param_mgr, CustomParameterManager, "Custom param");

    self->is_initialized = true;
    printf("BMS DataManager initialization completed with all method pointers\n");
}


/**
 * @brief 初始化BMS数据管理器实例（静态分配版本）
 * @param self BMS数据管理器实例指针
 * @return 0=成功，-1=失败
 */
int bms_data_manager_init(BMS_DataManager* self)
{
    if (self == NULL) return -1;

    /* 初始化基本数据成员 */
    self->is_initialized = false;

    /* 初始化方法指针 */
    self->methods.init = bms_data_mgr_init_method;

    printf("BMS DataManager basic initialization completed (static allocation)\n");
    return 0;
}
